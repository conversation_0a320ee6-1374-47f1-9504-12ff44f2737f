- config:
    name: 印章二级授权完整业务流程测试
    base_url: ${ENV(base_url)}
    variables:
        - sealGrantBizId: ${ENV(seal_grant_biz_id)}
        - orgId: ${ENV(org_id)}
        - grantedAccountId: ${ENV(granted_account_id)}
        - templateId: ${ENV(template_id)}
        - currentTime: ${get_timestamp()}

- test:
    name: 步骤1-查看初始二级授权列表
    variables:
        - params: "sealGrantBizId=${sealGrantBizId}&pageNum=1&pageSize=10&t=${currentTime}"
    api: api/seal/grant/second/list.yml
    extract:
        - initialTotal: content.data.total
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 步骤2-新增二级授权(指定成员+指定模板)
    variables:
        - params: "t=${currentTime}"
        - json: {"orgId":"${orgId}","sealGrantBizId":"${sealGrantBizId}","grantedAccountIds":["${grantedAccountId}"],"roleKey":"SEAL_USER","scopeList":["${templateId}"],"effectiveTime":${get_timestamp()},"expireTime":${get_future_timestamp(30)}}
    api: api/seal/grant/second/add.yml
    extract:
        - newSecGrantBizId: content.data.secSealGrantBizIds.0
        - flowId: content.data.flowId
        - signUrl: content.data.signUrl
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - type_match: ["content.data.secSealGrantBizIds", "list"]
        - length_greater_than: ["content.data.secSealGrantBizIds", 0]

- test:
    name: 步骤3-验证新增后列表数量增加
    variables:
        - params: "sealGrantBizId=${sealGrantBizId}&pageNum=1&pageSize=10&t=${currentTime}"
    api: api/seal/grant/second/list.yml
    extract:
        - newTotal: content.data.total
        - firstRecord: content.data.result.0
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - eq: ["${newTotal}", "${sum(${initialTotal}, 1)}"]

- test:
    name: 步骤4-设置审批通知为开启
    variables:
        - params: "secSealGrantBizId=${newSecGrantBizId}&notifySetting=true&t=${currentTime}"
    api: api/seal/grant/second/update-notify-setting.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 步骤5-验证通知设置生效
    variables:
        - params: "sealGrantBizId=${sealGrantBizId}&pageNum=1&pageSize=10&t=${currentTime}"
    api: api/seal/grant/second/list.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 步骤6-编辑二级授权有效期
    variables:
        - params: "t=${currentTime}"
        - json: {"orgId":"${orgId}","secSealGrantBizId":"${newSecGrantBizId}","effectiveTime":${get_timestamp()},"expireTime":${get_future_timestamp(60)}}
    api: api/seal/grant/second/update.yml
    extract:
        - updatedSecGrantBizId: content.data.secSealGrantBizId
        - updatedSignUrl: content.data.signUrl
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - eq: ["content.data.secSealGrantBizId", "${newSecGrantBizId}"]

- test:
    name: 步骤7-下载授权书
    variables:
        - params: "t=${currentTime}"
        - secSealGrantBizId: ${newSecGrantBizId}
    api: api/seal/grant/second/download.yml
    extract:
        - downloadUrl: content.data
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - contains: ["content.data", "https://"]
        - contains: ["content.data", "oss-cn-hangzhou.aliyuncs.com"]

- test:
    name: 步骤8-关闭审批通知
    variables:
        - params: "secSealGrantBizId=${newSecGrantBizId}&notifySetting=false&t=${currentTime}"
    api: api/seal/grant/second/update-notify-setting.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 步骤9-删除二级授权
    variables:
        - params: "t=${currentTime}"
        - secSealGrantBizId: ${newSecGrantBizId}
    api: api/seal/grant/second/delete.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 步骤10-验证删除后列表数量恢复
    variables:
        - params: "sealGrantBizId=${sealGrantBizId}&pageNum=1&pageSize=10&t=${currentTime}"
    api: api/seal/grant/second/list.yml
    extract:
        - finalTotal: content.data.total
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - eq: ["${finalTotal}", "${initialTotal}"]
