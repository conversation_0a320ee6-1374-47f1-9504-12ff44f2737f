- config:
    name: 印章二级授权异常场景测试
    base_url: ${ENV(base_url)}
    variables:
        - sealGrantBizId: ${ENV(seal_grant_biz_id)}
        - orgId: ${ENV(org_id)}
        - invalidId: "invalid_id_123"
        - currentTime: ${get_timestamp()}

- test:
    name: 查看二级授权列表-缺少必填参数
    variables:
        - params: "pageNum=1&pageSize=10&t=${currentTime}"
    api: api/seal/grant/second/list.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 查看二级授权列表-参数类型错误
    variables:
        - params: "sealGrantBizId=${sealGrantBizId}&pageNum=abc&pageSize=10&t=${currentTime}"
    api: api/seal/grant/second/list.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 新增二级授权-缺少必填参数
    variables:
        - params: "t=${currentTime}"
        - json: {"orgId":"${orgId}","sealGrantBizId":"${sealGrantBizId}"}
    api: api/seal/grant/second/add.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 新增二级授权-授权范围违规(全部成员+全部合同)
    variables:
        - params: "t=${currentTime}"
        - json: {"orgId":"${orgId}","sealGrantBizId":"${sealGrantBizId}","grantedAccountIds":["ALL"],"roleKey":"SEAL_USER","scopeList":["ALL"],"effectiveTime":${get_timestamp()},"expireTime":${get_future_timestamp(30)}}
    api: api/seal/grant/second/add.yml
    validate:
        - ne: ["content.code", 0]

- test:
    name: 新增二级授权-有效期超出一级授权范围
    variables:
        - params: "t=${currentTime}"
        - json: {"orgId":"${orgId}","sealGrantBizId":"${sealGrantBizId}","grantedAccountIds":["${ENV(granted_account_id)}"],"roleKey":"SEAL_USER","scopeList":["${ENV(template_id)}"],"effectiveTime":${get_timestamp()},"expireTime":${get_future_timestamp(365)}}
    api: api/seal/grant/second/add.yml
    validate:
        - ne: ["content.code", 0]
        - contains: ["content.message", "二级授权失效时间晚于了一级授权记录的失效时间"]

- test:
    name: 删除二级授权-无效ID
    variables:
        - params: "t=${currentTime}"
        - secSealGrantBizId: ${invalidId}
    api: api/seal/grant/second/delete.yml
    validate:
        - eq: ["status_code", 404]

- test:
    name: 下载授权书-无效ID
    variables:
        - params: "t=${currentTime}"
        - secSealGrantBizId: ${invalidId}
    api: api/seal/grant/second/download.yml
    validate:
        - eq: ["status_code", 404]

- test:
    name: 设置审批通知-参数类型错误
    variables:
        - params: "secSealGrantBizId=${invalidId}&notifySetting=invalid&t=${currentTime}"
    api: api/seal/grant/second/update-notify-setting.yml
    validate:
        - eq: ["status_code", 400]
