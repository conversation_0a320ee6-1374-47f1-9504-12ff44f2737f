- config:
    name: 印章二级授权正常流程测试
    base_url: ${ENV(base_url)}
    variables:
        - sealGrantBizId: ${ENV(seal_grant_biz_id)}
        - orgId: ${ENV(org_id)}
        - grantedAccountId: ${ENV(granted_account_id)}
        - templateId: ${ENV(template_id)}
        - currentTime: ${get_timestamp()}

- test:
    name: 查看二级授权列表
    variables:
        - params: "sealGrantBizId=${sealGrantBizId}&pageNum=1&pageSize=10&t=${currentTime}"
    api: api/seal/grant/second/list.yml
    extract:
        - total: content.data.total
        - secGrantBizId: content.data.result.0.secGrantBizId
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 新增二级授权
    variables:
        - params: "t=${currentTime}"
        - json: {"orgId":"${orgId}","sealGrantBizId":"${sealGrantBizId}","grantedAccountIds":["${grantedAccountId}"],"roleKey":"SEAL_USER","scopeList":["${templateId}"],"effectiveTime":${get_timestamp()},"expireTime":${get_future_timestamp(30)}}
    api: api/seal/grant/second/add.yml
    extract:
        - newSecGrantBizId: content.data.secSealGrantBizIds.0
        - flowId: content.data.flowId
        - signUrl: content.data.signUrl
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 设置审批通知
    variables:
        - params: "secSealGrantBizId=${newSecGrantBizId}&notifySetting=true&t=${currentTime}"
    api: api/seal/grant/second/update-notify-setting.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 编辑二级授权
    variables:
        - params: "t=${currentTime}"
        - json: {"orgId":"${orgId}","secSealGrantBizId":"${newSecGrantBizId}","effectiveTime":${get_timestamp()},"expireTime":${get_future_timestamp(60)}}
    api: api/seal/grant/second/update.yml
    extract:
        - updatedSecGrantBizId: content.data.secSealGrantBizId
        - updatedSignUrl: content.data.signUrl
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 下载授权书
    variables:
        - params: "t=${currentTime}"
        - secSealGrantBizId: ${newSecGrantBizId}
    api: api/seal/grant/second/download.yml
    extract:
        - downloadUrl: content.data
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - contains: ["content.data", "https://"]
