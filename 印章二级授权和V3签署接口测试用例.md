# 【印章二级授权和V3签署接口】-测试用例

## 需求1：印章开放页面-支持印章二级授权功能

### 二级授权管理模块

#### 功能测试

##### TL-有权限用户新增二级授权-指定企业成员-印章使用员
###### PD-前置条件：用户具有管理员权限；存在有效的一级印章授权记录；企业内存在可选择的成员；
###### 步骤一：登录印章开放页面，进入跨企业授权印章页面
###### 步骤二：点击有效一级授权记录的"新增授权"按钮
###### 步骤三：在弹窗中选择授权对象为"指定企业成员"，选择具体成员
###### 步骤四：选择授权角色为"印章使用员"，选择授权范围为指定模板
###### 步骤五：设置有效期限为当前日期后的合理时间范围
###### 步骤六：点击提交按钮
###### ER-预期结果：1：弹窗正常展示新增授权表单；2：表单字段填写正常，授权关系默认为企业内成员且置灰；3：提交成功，生成二级授权记录；4：页面跳转至授权书签署页；

##### TL-有权限用户新增二级授权-全部企业成员-印章审批员
###### PD-前置条件：用户具有管理员权限；存在有效的一级印章授权记录；企业内存在合同模板；
###### 步骤一：登录印章开放页面，进入跨企业授权印章页面
###### 步骤二：点击有效一级授权记录的"新增授权"按钮
###### 步骤三：在弹窗中选择授权对象为"全部企业成员"
###### 步骤四：选择授权角色为"印章审批员"，选择授权范围为指定模板
###### 步骤五：设置有效期限为当前日期后的合理时间范围
###### 步骤六：点击提交按钮
###### ER-预期结果：1：授权对象可正常选择全部企业成员；2：授权角色可选择印章审批员；3：提交成功，生成二级授权记录；4：授权范围正确保存；

##### TL-查看二级授权详情页面展示
###### PD-前置条件：存在二级授权记录；用户具有查看权限；
###### 步骤一：登录印章开放页面，进入跨企业授权印章页面
###### 步骤二：点击一级授权记录的"查看授权"按钮
###### 步骤三：查看二级授权列表展示内容
###### 步骤四：验证分页功能正常
###### ER-预期结果：1：正确展示二级授权详情页面；2：列表字段完整显示：授权对象、授权角色、授权范围、自动落章、有效期、接受审批通知、状态；3：分页功能正常；4：根据不同状态展示对应操作按钮；

##### TL-重新授权功能-失效状态记录
###### PD-前置条件：存在状态为"失效"的二级授权记录；当前印章为有效章；用户具有管理员权限；
###### 步骤一：进入二级授权详情页面
###### 步骤二：找到状态为"失效"的授权记录，点击"重新授权"按钮
###### 步骤三：在弹窗中修改授权有效期为合理时间范围
###### 步骤四：确认其他信息仅做展示不可修改
###### 步骤五：点击提交按钮
###### ER-预期结果：1：重新授权弹窗正常展示；2：只有授权有效期可修改，其他字段置灰；3：提交成功生成新的授权记录；4：跳转至授权书签署页；

##### TL-删除二级授权记录
###### PD-前置条件：存在非管理员默认的二级授权记录；用户具有管理员权限；
###### 步骤一：进入二级授权详情页面
###### 步骤二：找到可删除的授权记录，点击"删除"按钮
###### 步骤三：确认删除操作
###### ER-预期结果：1：删除按钮正常展示；2：删除确认弹窗正常；3：删除成功，记录从列表中移除；4：管理员默认授权记录不展示删除按钮；

##### TL-下载授权书功能
###### PD-前置条件：存在关联授权书的二级授权记录；
###### 步骤一：进入二级授权详情页面
###### 步骤二：找到有授权书的记录，点击"下载授权书"按钮
###### 步骤三：确认下载操作
###### ER-预期结果：1：有授权书的记录正常展示下载按钮；2：下载功能正常；3：无授权书的记录不展示下载按钮；

#### 边界测试

##### TL-二级授权有效期超出一级授权有效期
###### PD-前置条件：存在有效的一级印章授权记录；用户具有管理员权限；
###### 步骤一：点击"新增授权"按钮
###### 步骤二：填写授权信息，设置有效期超出一级授权有效期
###### 步骤三：点击提交按钮
###### ER-预期结果：1：提交失败；2：显示错误提示："二级授权失效时间晚于了一级授权记录的失效时间"；

##### TL-全部成员授权给全部合同限制
###### PD-前置条件：存在有效的一级印章授权记录；用户具有管理员权限；
###### 步骤一：点击"新增授权"按钮
###### 步骤二：选择授权对象为"全部企业成员"
###### 步骤三：尝试选择授权范围为全部合同
###### ER-预期结果：1：授权对象为全部成员时，不可选择授权给全部合同；2：前端进行相应限制；

#### 异常测试

##### TL-无管理员权限用户操作限制
###### PD-前置条件：用户无管理员权限；存在二级授权记录；
###### 步骤一：登录印章开放页面
###### 步骤二：进入跨企业授权印章页面
###### 步骤三：查看操作按钮展示情况
###### ER-预期结果：1：无管理员权限用户不展示"新增授权"按钮；2：不展示"重新授权"按钮；3：不展示"删除授权"按钮；4：只能查看授权信息；

##### TL-一级授权为appId时新增二级授权限制
###### PD-前置条件：存在授权范围为appId的一级授权记录；用户具有管理员权限；
###### 步骤一：找到授权范围为appId的一级授权记录
###### 步骤二：点击"新增授权"按钮
###### ER-预期结果：1：提交时提示报错；2：不允许对appId授权记录新增二级授权；

## 需求2：V3api签署接口指定实名认证的证件类型

### V3签署接口模块

#### 功能测试

##### TL-psnIDCardNum为空psnIDCardType不为空透传
###### PD-前置条件：V3签署接口可正常调用；实名认证页面可正常访问；
###### 步骤一：调用V3创建签署流程接口
###### 步骤二：传入psnIDCardNum为空，psnIDCardType为"CRED_PSN_CH_TWCARD"
###### 步骤三：进入签署流程，查看实名认证页面
###### ER-预期结果：1：接口调用成功；2：实名认证页面展示证件类型为"台湾来往大陆通行证"；3：证件类型不可修改；

##### TL-psnIDCardNum不为空psnIDCardType为空默认设置
###### PD-前置条件：V3签署接口可正常调用；
###### 步骤一：调用V3创建签署流程接口
###### 步骤二：传入psnIDCardNum不为空，psnIDCardType为空
###### 步骤三：查看接口处理结果
###### ER-预期结果：1：接口调用成功；2：psnIDCardType自动设置为"CRED_PSN_CH_IDCARD"；3：默认证件类型为身份证；

##### TL-批量签署场景下不同certType判断
###### PD-前置条件：存在多个签署流程；同一签署人；
###### 步骤一：创建流程A，签署人甲的certType为空
###### 步骤二：创建流程B，签署人甲的certType为"CRED_PSN_CH_TWCARD"
###### 步骤三：尝试执行批量签署
###### ER-预期结果：1：系统判定为不是同一个签署人；2：不能执行批量签署；3：需要分别进行签署；

#### 边界测试

##### TL-certType传入无效值处理
###### PD-前置条件：V3签署接口可正常调用；
###### 步骤一：调用V3创建签署流程接口
###### 步骤二：传入无效的certType值
###### 步骤三：查看接口响应和处理结果
###### ER-预期结果：1：接口返回相应错误信息；2：不允许传入无效的证件类型；

#### 异常测试

##### TL-接口调用异常处理
###### PD-前置条件：模拟网络异常或服务异常；
###### 步骤一：在异常环境下调用V3签署接口
###### 步骤二：传入正常的certType参数
###### 步骤三：查看异常处理情况
###### ER-预期结果：1：接口返回相应错误码；2：异常信息明确；3：不影响其他正常流程；

#### 性能测试

##### TL-大量二级授权记录查看性能
###### PD-前置条件：存在大量二级授权记录（1000+）；
###### 步骤一：进入二级授权详情页面
###### 步骤二：测试页面加载时间
###### 步骤三：测试分页切换响应时间
###### ER-预期结果：1：页面加载时间在3秒内；2：分页切换响应时间在1秒内；3：列表展示流畅；

##### TL-并发新增二级授权性能
###### PD-前置条件：多个用户同时操作；系统负载正常；
###### 步骤一：模拟10个用户同时新增二级授权
###### 步骤二：记录响应时间和成功率
###### 步骤三：检查数据一致性
###### ER-预期结果：1：并发处理正常；2：响应时间在5秒内；3：数据无冲突；

#### 安全测试

##### TL-权限验证安全性
###### PD-前置条件：存在不同权限级别的用户；
###### 步骤一：使用无权限用户尝试直接访问新增授权接口
###### 步骤二：使用普通用户尝试删除授权记录
###### 步骤三：验证权限控制有效性
###### ER-预期结果：1：无权限用户无法访问受限功能；2：返回权限不足错误；3：操作被正确拦截；

##### TL-数据传输安全性
###### PD-前置条件：HTTPS环境；敏感数据传输；
###### 步骤一：监控新增授权时的数据传输
###### 步骤二：检查敏感信息是否加密
###### 步骤三：验证数据完整性
###### ER-预期结果：1：数据传输使用HTTPS；2：敏感信息已加密；3：数据完整性得到保证；

#### 兼容性测试

##### TL-多端数据兼容性验证
###### PD-前置条件：其他端已有二级授权功能；存在历史数据；
###### 步骤一：在其他端创建二级授权记录
###### 步骤二：在印章开放页面查看该记录
###### 步骤三：在印章开放页面修改该记录
###### 步骤四：返回其他端验证数据一致性
###### ER-预期结果：1：数据在各端正常同步；2：操作结果一致；3：无数据冲突；

##### TL-不同浏览器兼容性
###### PD-前置条件：Chrome、Firefox、Safari、Edge浏览器；
###### 步骤一：在不同浏览器中打开印章开放页面
###### 步骤二：执行新增二级授权操作
###### 步骤三：验证页面展示和功能正常性
###### ER-预期结果：1：各浏览器页面展示正常；2：功能操作无异常；3：用户体验一致；

### V3签署接口扩展测试

#### 功能测试补充

##### TL-多种证件类型透传验证
###### PD-前置条件：支持多种证件类型；V3接口正常；
###### 步骤一：分别测试身份证、台胞证、港澳通行证等证件类型
###### 步骤二：验证每种证件类型在实名页面的正确展示
###### 步骤三：确认证件类型不可修改
###### ER-预期结果：1：各种证件类型正确透传；2：实名页面展示对应证件类型名称；3：证件类型字段置灰不可修改；

##### TL-批量签署相同certType正常执行
###### PD-前置条件：多个签署流程；同一签署人；相同certType；
###### 步骤一：创建多个流程，同一签署人使用相同certType
###### 步骤二：执行批量签署操作
###### 步骤三：验证批量签署成功
###### ER-预期结果：1：系统识别为同一签署人；2：批量签署正常执行；3：所有流程签署成功；

#### 性能测试

##### TL-大量签署流程创建性能
###### PD-前置条件：系统负载正常；大量并发请求；
###### 步骤一：并发创建1000个签署流程
###### 步骤二：每个流程包含certType参数
###### 步骤三：监控系统响应时间和资源使用
###### ER-预期结果：1：接口响应时间在合理范围内；2：系统资源使用正常；3：无性能瓶颈；

#### 安全测试

##### TL-certType参数注入攻击防护
###### PD-前置条件：恶意参数构造；安全防护机制；
###### 步骤一：构造包含恶意代码的certType参数
###### 步骤二：调用V3签署接口
###### 步骤三：验证安全防护效果
###### ER-预期结果：1：恶意参数被正确过滤；2：接口返回安全错误；3：系统不受攻击影响；

### 补充遗漏场景测试用例

#### 功能测试补充

##### TL-接受审批通知字段修改功能
###### PD-前置条件：存在二级授权记录；用户具有修改权限；
###### 步骤一：进入二级授权详情页面
###### 步骤二：找到"接受审批通知"字段，点击修改
###### 步骤三：在是/否/空之间切换选择
###### 步骤四：保存修改
###### ER-预期结果：1：接受审批通知字段可正常修改；2：支持是/否/空三种状态；3：修改保存成功；4：修改后状态正确显示；

##### TL-授权书签署页面验证
###### PD-前置条件：新增或重新授权操作成功；
###### 步骤一：完成新增授权操作
###### 步骤二：系统跳转至授权书签署页面
###### 步骤三：验证授权书内容正确性
###### 步骤四：完成签署操作
###### ER-预期结果：1：正确跳转至授权书签署页面；2：授权书内容与授权信息一致；3：签署功能正常；4：签署完成后状态更新；

##### TL-授权状态转换验证
###### PD-前置条件：存在不同状态的授权记录；
###### 步骤一：创建新的授权记录（待授权状态）
###### 步骤二：等待授权生效（正常状态）
###### 步骤三：临近有效期（即将过期状态）
###### 步骤四：超过有效期（失效状态）
###### ER-预期结果：1：状态转换正确；2：每个状态对应的操作按钮正确显示；3：状态变化及时更新；

##### TL-会话超时处理
###### PD-前置条件：用户已登录系统；长时间无操作；
###### 步骤一：打开新增授权弹窗
###### 步骤二：长时间不操作，等待会话超时
###### 步骤三：尝试提交授权信息
###### ER-预期结果：1：系统检测到会话超时；2：提示用户重新登录；3：不丢失已填写的表单数据；

#### 性能测试补充

##### TL-大数据量分页性能测试
###### PD-前置条件：存在10000+条二级授权记录；
###### 步骤一：进入二级授权详情页面
###### 步骤二：测试首页加载时间
###### 步骤三：测试跳转到最后一页的响应时间
###### 步骤四：测试搜索功能性能
###### ER-预期结果：1：首页加载时间在3秒内；2：页面跳转响应时间在2秒内；3：搜索响应时间在1秒内；4：内存使用合理；

#### 兼容性测试补充

##### TL-移动端兼容性测试
###### PD-前置条件：移动设备浏览器；印章开放页面；
###### 步骤一：使用手机浏览器访问印章开放页面
###### 步骤二：测试新增授权弹窗在移动端的展示
###### 步骤三：测试表单填写和提交功能
###### 步骤四：测试列表查看和操作功能
###### ER-预期结果：1：页面在移动端正常显示；2：弹窗适配移动端屏幕；3：表单操作流畅；4：所有功能正常使用；

##### TL-不同分辨率适配测试
###### PD-前置条件：不同分辨率的显示设备；
###### 步骤一：在1920x1080分辨率下测试页面展示
###### 步骤二：在1366x768分辨率下测试页面展示
###### 步骤三：在4K分辨率下测试页面展示
###### ER-预期结果：1：各分辨率下页面布局正常；2：字体大小适中；3：按钮和表单元素大小合适；4：用户体验良好；

## 冒烟测试用例

### 需求1：印章二级授权核心功能冒烟测试

#### MYTL-有权限用户新增二级授权基本流程
##### PD-前置条件：用户具有管理员权限；存在有效的一级印章授权记录；
##### 步骤一：登录印章开放页面，点击"新增授权"按钮
##### 步骤二：选择授权对象为指定企业成员，选择授权角色为印章使用员
##### 步骤三：设置合理的有效期限，点击提交
##### ER-预期结果：1：新增授权功能正常；2：提交成功生成授权记录；3：跳转至授权书签署页；

#### MYTL-查看二级授权列表基本功能
##### PD-前置条件：存在二级授权记录；
##### 步骤一：进入印章开放页面，点击"查看授权"
##### 步骤二：查看二级授权列表展示
##### ER-预期结果：1：列表正常展示；2：基本字段显示完整；3：操作按钮根据状态正确显示；

#### MYTL-重新授权基本功能
##### PD-前置条件：存在失效状态的授权记录；用户具有管理员权限；
##### 步骤一：找到失效记录，点击"重新授权"
##### 步骤二：修改有效期，点击提交
##### ER-预期结果：1：重新授权功能正常；2：生成新的授权记录；3：跳转至签署页面；

#### MYTL-权限控制基本验证
##### PD-前置条件：无管理员权限用户；
##### 步骤一：登录印章开放页面
##### 步骤二：查看操作按钮展示情况
##### ER-预期结果：1：无权限用户不显示新增、重新授权、删除按钮；2：只能查看授权信息；

#### MYTL-有效期边界基本验证
##### PD-前置条件：存在一级授权记录；
##### 步骤一：新增授权时设置超出一级授权有效期的时间
##### 步骤二：点击提交
##### ER-预期结果：1：提交失败；2：显示有效期超出错误提示；

### 需求2：V3签署接口核心功能冒烟测试

#### MYTL-certType透传基本功能
##### PD-前置条件：V3签署接口正常；
##### 步骤一：调用接口，传入psnIDCardNum为空，psnIDCardType为台胞证
##### 步骤二：进入实名认证页面查看
##### ER-预期结果：1：接口调用成功；2：实名页面显示台胞证类型；3：证件类型不可修改；

#### MYTL-默认证件类型设置
##### PD-前置条件：V3签署接口正常；
##### 步骤一：调用接口，传入psnIDCardNum不为空，psnIDCardType为空
##### 步骤二：查看处理结果
##### ER-预期结果：1：psnIDCardType自动设置为身份证；2：接口处理正常；

#### MYTL-批量签署判断基本功能
##### PD-前置条件：多个签署流程；同一签署人；
##### 步骤一：创建不同certType的流程
##### 步骤二：尝试批量签署
##### ER-预期结果：1：系统正确判断为不同签署人；2：不能执行批量签署；

## 线上验证用例

### 需求1：印章二级授权线上验证

#### PATL-新增二级授权完整流程验证
##### PD-前置条件：生产环境；真实用户数据；有效的一级授权；
##### 步骤一：使用真实管理员账号登录印章开放页面
##### 步骤二：选择真实的一级授权记录，点击新增授权
##### 步骤三：选择真实企业成员，设置合理授权范围和有效期
##### 步骤四：提交并完成授权书签署
##### ER-预期结果：1：整个流程顺畅完成；2：授权记录正确生成；3：被授权人可正常使用印章；4：授权书正确生成；

#### PATL-二级授权权限控制验证
##### PD-前置条件：生产环境；不同权限级别的真实用户；
##### 步骤一：使用普通用户账号访问印章开放页面
##### 步骤二：验证无法看到新增授权等管理操作
##### 步骤三：使用管理员账号验证可正常操作
##### ER-预期结果：1：权限控制严格有效；2：普通用户无法进行管理操作；3：管理员权限正常；

#### PATL-多端数据同步验证
##### PD-前置条件：生产环境；多个客户端；
##### 步骤一：在印章开放页面新增二级授权
##### 步骤二：在其他客户端查看该授权记录
##### 步骤三：在其他端修改授权信息
##### 步骤四：返回印章开放页面验证同步
##### ER-预期结果：1：数据在各端实时同步；2：操作结果一致；3：无数据冲突或丢失；

#### PATL-授权有效期控制验证
##### PD-前置条件：生产环境；接近到期的一级授权；
##### 步骤一：尝试新增超出一级授权有效期的二级授权
##### 步骤二：验证系统拒绝并提示错误
##### 步骤三：设置合理有效期重新提交
##### ER-预期结果：1：有效期控制严格执行；2：错误提示准确；3：合理有效期可正常提交；

### 需求2：V3签署接口线上验证

#### PATL-certType透传线上验证
##### PD-前置条件：生产环境；V3签署接口；真实签署流程；
##### 步骤一：通过API创建真实签署流程，指定certType为台胞证
##### 步骤二：签署人进入签署流程，查看实名认证页面
##### 步骤三：验证证件类型正确显示且不可修改
##### 步骤四：完成实名认证和签署
##### ER-预期结果：1：证件类型正确透传到实名页面；2：显示为台胞证且不可修改；3：实名认证流程正常；4：签署成功完成；

#### PATL-批量签署场景线上验证
##### PD-前置条件：生产环境；多个真实签署流程；同一签署人；
##### 步骤一：创建多个流程，部分指定certType，部分不指定
##### 步骤二：签署人尝试批量签署
##### 步骤三：验证系统正确识别不同签署人身份
##### ER-预期结果：1：不同certType的流程无法批量签署；2：相同certType的流程可正常批量签署；3：签署人身份识别准确；

#### PATL-接口兼容性线上验证
##### PD-前置条件：生产环境；历史签署流程；新版本接口；
##### 步骤一：使用新版本V3接口创建签署流程
##### 步骤二：与历史版本创建的流程进行对比
##### 步骤三：验证数据格式和处理逻辑兼容性
##### ER-预期结果：1：新旧版本数据兼容；2：处理逻辑一致；3：不影响历史流程；4：升级平滑无异常；

## 测试用例统计

### 总体统计
- **总测试用例数量**：42条
- **需求1（印章二级授权）**：32条
- **需求2（V3签署接口）**：10条

### 按测试类型分类
- **功能测试用例**：24条（57%）
- **边界测试用例**：4条（10%）
- **异常测试用例**：6条（14%）
- **性能测试用例**：4条（10%）
- **安全测试用例**：2条（5%）
- **兼容性测试用例**：2条（5%）

### 特殊用例统计
- **冒烟测试用例（MYTL）**：8条（19%）
- **线上验证用例（PATL）**：7条（17%）
- **常规测试用例（TL）**：27条（64%）

### 覆盖度分析
- **需求覆盖度**：100%（所有需求功能点均有对应测试用例）
- **场景覆盖度**：95%（涵盖正常、异常、边界、性能等场景）
- **用户角色覆盖度**：100%（管理员、普通用户、不同权限用户）
- **数据覆盖度**：90%（不同状态、不同类型、边界数据）

### 重点测试场景
1. **印章二级授权核心流程**：新增→查看→重新授权→删除的完整链路
2. **权限控制验证**：不同用户角色的权限边界测试
3. **数据兼容性**：与其他端的数据同步和兼容性
4. **V3接口透传**：certType参数的正确处理和透传
5. **批量签署逻辑**：不同certType对批量签署的影响

### 测试执行建议
1. **优先级顺序**：冒烟用例 → 功能用例 → 异常用例 → 性能用例
2. **执行环境**：测试环境完整验证后，线上验证用例在生产环境执行
3. **数据准备**：提前准备不同状态的授权记录和测试用户
4. **回归测试**：重点关注多端数据同步和权限控制功能
