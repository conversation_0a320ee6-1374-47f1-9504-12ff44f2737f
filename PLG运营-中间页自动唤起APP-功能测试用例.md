# PLG运营-中间页自动唤起APP-功能测试用例

## 功能测试

### APP检测与唤起逻辑

#### TL-H5中间页APP已安装且不在灰度黑名单正常唤起验证

##### PD-前置条件：手机已安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：打开签署中间页

##### 步骤二：验证APP唤起提示弹窗

##### 步骤三：点击"打开APP"按钮

##### ER-预期结果：1：页面弹出引导打开APP的提示窗；2：点击后成功唤起e签宝APP；3：APP正常打开并跳转到对应功能页面；

#### TL-H5中间页APP未安装不触发唤起逻辑验证

##### PD-前置条件：手机未安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：打开签署中间页

##### 步骤二：验证页面是否有唤起提示

##### ER-预期结果：1：页面不显示任何引导打开APP的提示；2：保持原有H5页面正常显示；3：用户可正常在H5页面完成操作；

#### TL-H5中间页appId在灰度黑名单中不触发唤起验证

##### PD-前置条件：手机已安装e签宝APP；当前appId在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：运营后台将测试appId添加到功能灰度黑名单

##### 步骤二：打开签署中间页

##### 步骤三：验证页面是否有唤起提示

##### ER-预期结果：1：页面保持原样，不显示引导提示；2：用户可正常使用H5功能；

### 业务场景验证

#### TL-审批中间页APP唤起功能验证

##### PD-前置条件：手机已安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：打开审批中间页

##### 步骤二：验证唤起提示展示

##### 步骤三：点击引导提示打开APP

##### ER-预期结果：1：显示审批场景的引导提示；2：成功唤起APP并跳转到审批功能；3：审批流程正常进行；

### 操作系统兼容性

#### TL-鸿蒙系统APP唤起功能兼容性验证

##### PD-前置条件：使用鸿蒙系统手机；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：在鸿蒙系统手机浏览器中打开签署中间页

##### 步骤二：验证APP唤起功能

##### 步骤三：点击唤起提示

##### ER-预期结果：1：唤起逻辑兼容鸿蒙系统；2：成功唤起e签宝APP；3：APP在鸿蒙系统中正常运行；

#### TL-iOS系统不触发APP唤起功能验证

##### PD-前置条件：使用iOS系统手机；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：在iOS系统手机浏览器中打开签署中间页

##### 步骤二：验证页面是否有唤起处理

##### ER-预期结果：1：页面不显示任何唤起提示；2：H5页面功能正常可用；3：用户可在H5完成签署操作；

### 浏览器兼容性

#### TL-Chrome浏览器APP唤起功能验证

##### PD-前置条件：使用安卓系统Chrome浏览器；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：打开Chrome浏览器访问签署中间页

##### 步骤二：验证唤起提示展示

##### 步骤三：点击唤起提示

##### ER-预期结果：1：唤起提示在Chrome中正确显示；2：Chrome浏览器成功唤起APP；3：整个流程在Chrome中运行稳定；

#### TL-微信内置浏览器APP唤起功能验证

##### PD-前置条件：在微信内置浏览器中访问；使用安卓系统；手机已安装e签宝APP；appId不在灰度黑名单中；

##### 步骤一：通过微信打开签署中间页链接

##### 步骤二：验证微信环境下的唤起效果

##### 步骤三：验证从微信跳转到APP的流程

##### ER-预期结果：1：唤起逻辑适配微信环境；2：成功从微信跳转到e签宝APP；3：跳转后APP功能正常；

#### TL-UC浏览器APP唤起功能验证

##### PD-前置条件：使用安卓系统UC浏览器；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：打开UC浏览器访问签署中间页

##### 步骤二：验证UC浏览器中的唤起提示

##### 步骤三：点击提示验证APP唤起效果

##### ER-预期结果：1：唤起逻辑兼容UC浏览器；2：成功唤起e签宝APP；3：UC浏览器到APP的跳转流畅；

#### TL-QQ浏览器APP唤起功能验证

##### PD-前置条件：使用安卓系统QQ浏览器；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：打开QQ浏览器访问审批中间页

##### 步骤二：验证QQ浏览器中的唤起提示展示

##### 步骤三：测试从QQ浏览器唤起APP的效果

##### ER-预期结果：1：唤起提示在QQ浏览器正确显示；2：成功从QQ浏览器唤起APP；3：APP打开后功能正常可用；

### 异常场景处理

#### TL-网络异常情况下APP检测功能验证

##### PD-前置条件：手机已安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接不稳定；

##### 步骤一：在网络信号较弱环境下打开签署中间页

##### 步骤二：验证检测失败时的降级处理

##### ER-预期结果：1：检测失败时不显示唤起提示；2：页面保持正常H5功能可用；3：不影响用户正常使用H5完成操作；

### 灰度配置管理

#### TL-灰度配置实时生效验证

##### PD-前置条件：手机已安装e签宝APP；使用安卓系统；网络连接正常；运营后台可正常操作；

##### 步骤一：确认当前appId不在灰度黑名单中，验证唤起功能正常

##### 步骤二：运营后台将该appId添加到灰度黑名单

##### 步骤三：刷新页面验证灰度配置变更效果

##### 步骤四：运营后台将该appId从黑名单中移除，再次验证

##### ER-预期结果：1：添加黑名单后唤起功能被禁用；2：配置变更实时生效；3：移除黑名单后唤起功能恢复；

### 性能验证

#### TL-多次访问中间页APP检测性能验证

##### PD-前置条件：手机已安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：第一次打开签署中间页，记录APP检测响应时间

##### 步骤二：连续多次打开关闭页面，记录每次检测时间

##### 步骤三：验证检测结果的准确性和一致性

##### ER-预期结果：1：首次检测响应时间小于2秒；2：多次检测结果保持一致；3：检测性能稳定不影响页面加载；
