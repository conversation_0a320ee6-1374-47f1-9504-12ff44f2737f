# 认证相关需求测试用例

## 需求1：【认证】核身接口增加传参控制是否展示协议

### 功能测试

#### 参数控制逻辑

##### TL-个人核身网页接口showAgreement参数为true时协议展示验证

###### PD-前置条件：用户已登录；具有核身权限；协议服务正常；

###### 步骤一：调用个人核身网页接口

###### 步骤二：设置showAgreement参数为true

###### 步骤三：提交核身请求

###### 步骤四：检查返回的核身页面

###### 步骤五：验证协议展示状态

###### ER-预期结果：1：接口调用成功；2：核身页面正常展示；3：页面中显示数字证书协议、服务协议和隐私政策；4：协议链接可正常点击访问；5：短信中包含协议链接；

##### TL-个人核身网页接口showAgreement参数为false时协议不展示验证

###### PD-前置条件：用户已登录；具有核身权限；协议服务正常；

###### 步骤一：调用个人核身网页接口

###### 步骤二：设置showAgreement参数为false

###### 步骤三：提交核身请求

###### 步骤四：检查返回的核身页面

###### 步骤五：验证协议展示状态

###### ER-预期结果：1：接口调用成功；2：核身页面正常展示；3：页面中不显示任何协议内容；4：短信中不包含协议链接；

##### TL-个人核身网页接口showAgreement参数未传时默认值验证

###### PD-前置条件：用户已登录；具有核身权限；协议服务正常；

###### 步骤一：调用个人核身网页接口

###### 步骤二：不传showAgreement参数

###### 步骤三：提交核身请求

###### 步骤四：检查返回的核身页面

###### 步骤五：验证协议展示状态

###### ER-预期结果：1：接口调用成功；2：核身页面正常展示；3：页面中不显示任何协议内容；4：系统默认showAgreement为false；5：短信中不包含协议链接；

#### 多接口覆盖验证

##### TL-个人核身扫脸api接口showAgreement参数功能验证

###### PD-前置条件：用户已登录；具有核身权限；摄像头设备正常；

###### 步骤一：调用个人核身扫脸api接口

###### 步骤二：设置showAgreement参数为true

###### 步骤三：提交核身请求

###### 步骤四：进入扫脸核身流程

###### 步骤五：检查协议展示情况

###### ER-预期结果：1：接口调用成功；2：扫脸页面正常展示；3：页面中显示相关协议内容；4：协议优先级高于签署侧校验；

##### TL-个人核身三要素api接口showAgreement参数功能验证

###### PD-前置条件：用户已登录；具有核身权限；三要素信息准备完整；

###### 步骤一：调用个人核身三要素api接口

###### 步骤二：设置showAgreement参数为true

###### 步骤三：输入姓名、身份证号、手机号

###### 步骤四：提交核身请求

###### 步骤五：检查协议展示情况

###### ER-预期结果：1：接口调用成功；2：三要素验证正常进行；3：页面中显示相关协议内容；4：协议展示不影响核身流程；

##### TL-个人核身四要素api接口showAgreement参数功能验证

###### PD-前置条件：用户已登录；具有核身权限；四要素信息准备完整；

###### 步骤一：调用个人核身四要素api接口

###### 步骤二：设置showAgreement参数为true

###### 步骤三：输入姓名、身份证号、手机号、银行卡号

###### 步骤四：提交核身请求

###### 步骤五：检查协议展示情况

###### ER-预期结果：1：接口调用成功；2：四要素验证正常进行；3：页面中显示相关协议内容；4：根据认证用途展示企业证书协议；

### 边界测试

#### 参数边界验证

##### TL-showAgreement参数类型错误边界验证

###### PD-前置条件：用户已登录；具有核身权限；

###### 步骤一：调用个人核身网页接口

###### 步骤二：设置showAgreement参数为字符串"true"

###### 步骤三：提交核身请求

###### 步骤四：检查接口返回结果

###### ER-预期结果：1：接口返回参数类型错误提示；2：核身流程不执行；3：错误信息明确指出参数类型问题；

##### TL-showAgreement参数为null值边界验证

###### PD-前置条件：用户已登录；具有核身权限；

###### 步骤一：调用个人核身网页接口

###### 步骤二：设置showAgreement参数为null

###### 步骤三：提交核身请求

###### 步骤四：检查接口处理结果

###### ER-预期结果：1：接口正常处理；2：系统将null值处理为默认值false；3：协议不展示；

### 异常测试

#### 服务异常场景

##### TL-协议服务异常时showAgreement为true的处理验证

###### PD-前置条件：用户已登录；具有核身权限；协议服务异常；

###### 步骤一：调用个人核身网页接口

###### 步骤二：设置showAgreement参数为true

###### 步骤三：提交核身请求

###### 步骤四：检查系统处理结果

###### ER-预期结果：1：核身流程正常进行；2：协议展示失败不影响核身功能；3：系统记录协议服务异常日志；4：用户收到友好提示信息；

## 需求2：V3账号解绑链接支持微信小程序redirectUrl

### 功能测试

#### 微信小程序跳转逻辑

##### TL-V3账号解绑接口支持wechat://back参数验证

###### PD-前置条件：用户已在微信小程序中登录；账号已绑定；解绑服务正常；

###### 步骤一：在微信小程序中调用V3账号解绑接口

###### 步骤二：设置redirectUrl参数为"wechat://back"

###### 步骤三：执行解绑操作

###### 步骤四：确认解绑成功

###### 步骤五：检查页面跳转结果

###### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：成功唤起微信back方法；4：用户返回到上一页面；

##### TL-V3账号解绑接口普通redirectUrl参数验证

###### PD-前置条件：用户已登录；账号已绑定；解绑服务正常；

###### 步骤一：调用V3账号解绑接口

###### 步骤二：设置redirectUrl参数为普通URL地址

###### 步骤三：执行解绑操作

###### 步骤四：确认解绑成功

###### 步骤五：检查页面跳转结果

###### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：页面跳转到指定URL；4：跳转功能正常；

### 边界测试

#### 参数边界验证

##### TL-redirectUrl参数为空时解绑功能验证

###### PD-前置条件：用户已登录；账号已绑定；解绑服务正常；

###### 步骤一：调用V3账号解绑接口

###### 步骤二：不传redirectUrl参数或传空值

###### 步骤三：执行解绑操作

###### 步骤四：确认解绑结果

###### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：使用默认跳转逻辑；4：不影响解绑核心功能；

### 异常测试

#### 异常场景处理

##### TL-微信小程序环境异常时wechat://back处理验证

###### PD-前置条件：用户已登录；账号已绑定；非微信小程序环境；

###### 步骤一：在非微信小程序环境调用V3账号解绑接口

###### 步骤二：设置redirectUrl参数为"wechat://back"

###### 步骤三：执行解绑操作

###### 步骤四：检查系统处理结果

###### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：系统识别环境不匹配；4：使用降级跳转方案；5：不影响解绑功能；

## 需求3：国际短信扣费子产品上游适配

### 功能测试

#### 短信扣费区分逻辑

##### TL-国内手机号发送短信扣费验证

###### PD-前置条件：用户账户余额充足；短信服务正常；扣费系统正常；

###### 步骤一：使用国内手机号发起意愿认证短信

###### 步骤二：调用/v1/willingness/sys_transId/createCodeAuth接口

###### 步骤三：发送短信验证码

###### 步骤四：检查扣费记录

###### 步骤五：验证扣费金额

###### ER-预期结果：1：短信发送成功；2：按国内短信标准扣费；3：扣费记录准确；4：用户收到验证码；

##### TL-国际手机号发送短信扣费验证

###### PD-前置条件：用户账户余额充足；短信服务正常；扣费系统正常；国际短信子产品已开通；

###### 步骤一：使用国际手机号发起意愿认证短信

###### 步骤二：调用/v1/willingness/sys_transId/createCodeAuth接口

###### 步骤三：发送短信验证码

###### 步骤四：检查扣费记录

###### 步骤五：验证扣费金额

###### ER-预期结果：1：短信发送成功；2：按国际短信标准扣费；3：扣费金额为国内短信的十几二十倍；4：扣费子产品code为service-C-127；5：用户收到验证码；

#### 客户白名单控制

##### TL-存量客户白名单控制国际短信扣费验证

###### PD-前置条件：客户在白名单中；用户账户余额充足；短信服务正常；

###### 步骤一：白名单客户使用国际手机号发送短信

###### 步骤二：调用意愿认证短信接口

###### 步骤三：发送短信验证码

###### 步骤四：检查扣费记录

###### ER-预期结果：1：短信发送成功；2：按普通短信费用扣费；3：不按国际短信标准收费；4：白名单控制生效；

##### TL-非白名单客户国际短信扣费验证

###### PD-前置条件：客户不在白名单中；用户账户余额充足；短信服务正常；

###### 步骤一：非白名单客户使用国际手机号发送短信

###### 步骤二：调用意愿认证短信接口

###### 步骤三：发送短信验证码

###### 步骤四：检查扣费记录

###### ER-预期结果：1：短信发送成功；2：按国际短信费用扣费；3：扣费金额较高；4：默认收费逻辑生效；

### 边界测试

#### 手机号边界验证

##### TL-边界国家手机号短信扣费验证

###### PD-前置条件：用户账户余额充足；短信服务正常；

###### 步骤一：使用港澳台手机号发送短信

###### 步骤二：调用意愿认证短信接口

###### 步骤三：检查系统识别结果

###### 步骤四：验证扣费标准

###### ER-预期结果：1：系统正确识别手机号归属；2：按相应标准扣费；3：扣费逻辑准确；

### 异常测试

#### 扣费异常场景

##### TL-账户余额不足国际短信发送验证

###### PD-前置条件：用户账户余额不足；短信服务正常；

###### 步骤一：使用国际手机号发送短信

###### 步骤二：调用意愿认证短信接口

###### 步骤三：检查系统处理结果

###### ER-预期结果：1：短信发送失败；2：返回余额不足提示；3：不扣费；4：用户收到明确错误信息；

## 需求4：企业法人意愿认证方式调整

### 功能测试

#### 认证方式调整

##### TL-企业法人刷脸加四要素认证方式验证

###### PD-前置条件：企业账户已注册；法人信息完整；刷脸服务正常；

###### 步骤一：访问v2版本企业实名页面

###### 步骤二：选择企业法人认证方式

###### 步骤三：进行法人刷脸认证

###### 步骤四：输入组织机构四要素信息

###### 步骤五：提交认证申请

###### ER-预期结果：1：认证流程正常进行；2：法人刷脸认证成功；3：组织机构四要素信息比对通过；4：认证方式显示为"法人刷脸+组织机构四要素信息比对"；

##### TL-认证详情显示调整验证

###### PD-前置条件：企业已完成新认证方式；认证记录存在；

###### 步骤一：登录运营支撑平台

###### 步骤二：查看企业实名详情

###### 步骤三：检查认证管理子流程类型

###### 步骤四：验证认证方式描述

###### ER-预期结果：1：认证详情正常显示；2：子流程类型显示为"法人刷脸+组织机构四要素信息比对"；3：不再显示"组织机构三要素信息比对"；4：认证信息更加严谨；

#### 出证信息调整

##### TL-存证出证信息调整验证

###### PD-前置条件：企业已完成新认证方式；存证服务正常；

###### 步骤一：触发存证出证流程

###### 步骤二：生成认证证书

###### 步骤三：检查证书内容

###### 步骤四：验证认证方式描述

###### ER-预期结果：1：证书生成成功；2：证书中认证方式描述为"法人刷脸+组织机构四要素信息比对"；3：认证信息准确完整；4：证书具有法律效力；

### 边界测试

#### 认证流程边界

##### TL-法人刷脸失败时认证流程验证

###### PD-前置条件：企业账户已注册；法人信息完整；

###### 步骤一：访问v2版本企业实名页面

###### 步骤二：选择企业法人认证方式

###### 步骤三：进行法人刷脸认证（故意失败）

###### 步骤四：检查系统处理结果

###### ER-预期结果：1：刷脸认证失败；2：系统提示重新认证；3：不进入四要素验证环节；4：整体认证流程中断；

### 异常测试

#### 认证异常场景

##### TL-组织机构四要素信息错误时认证验证

###### PD-前置条件：企业账户已注册；法人刷脸认证成功；

###### 步骤一：进入组织机构四要素验证环节

###### 步骤二：输入错误的组织机构信息

###### 步骤三：提交认证申请

###### 步骤四：检查系统处理结果

###### ER-预期结果：1：四要素验证失败；2：系统提示信息错误；3：认证流程不通过；4：用户可重新输入正确信息；

## 需求5：SSO登录支持人机校验

### 功能测试

#### 人机校验集成

##### TL-SSO登录人机校验完整流程验证

###### PD-前置条件：用户未登录；极验服务正常；SSO服务正常；

###### 步骤一：访问SSO登录页面

###### 步骤二：调用人机校验接口获取challenge

###### 步骤三：完成极验人机校验

###### 步骤四：调用发送验证码接口并传入极验参数

###### 步骤五：输入验证码完成绑定登录

###### ER-预期结果：1：人机校验接口调用成功；2：极验组件正常展示；3：人机校验通过；4：验证码发送成功；5：SSO登录成功；

##### TL-极验参数校验逻辑验证

###### PD-前置条件：用户未登录；极验服务正常；

###### 步骤一：调用人机校验接口获取参数

###### 步骤二：完成人机校验获取校验结果

###### 步骤三：调用发送验证码接口

###### 步骤四：传入geetest_challenge、geetest_validate、geetest_seccode参数

###### 步骤五：检查后端校验结果

###### ER-预期结果：1：极验参数格式正确；2：后端校验通过；3：验证码发送成功；4：参数传递无误；

#### 极验降级处理

##### TL-极验服务异常时降级处理验证

###### PD-前置条件：用户未登录；极验服务异常；SSO服务正常；

###### 步骤一：访问SSO登录页面

###### 步骤二：尝试调用人机校验接口

###### 步骤三：检查系统降级处理

###### 步骤四：直接调用发送验证码接口

###### 步骤五：完成登录流程

###### ER-预期结果：1：系统检测到极验服务异常；2：自动启用降级模式；3：不展示极验组件；4：后端不进行极验校验；5：登录流程正常进行；

### 边界测试

#### 参数边界验证

##### TL-极验参数缺失时接口处理验证

###### PD-前置条件：用户未登录；极验服务正常；

###### 步骤一：调用发送验证码接口

###### 步骤二：只传入部分极验参数

###### 步骤三：检查接口返回结果

###### ER-预期结果：1：接口返回参数不完整错误；2：验证码发送失败；3：错误信息明确指出缺失参数；

##### TL-极验参数格式错误时接口处理验证

###### PD-前置条件：用户未登录；极验服务正常；

###### 步骤一：调用发送验证码接口

###### 步骤二：传入格式错误的极验参数

###### 步骤三：检查接口返回结果

###### ER-预期结果：1：接口返回参数格式错误；2：验证码发送失败；3：系统记录错误日志；

### 异常测试

#### 异常场景处理

##### TL-人机校验超时时系统处理验证

###### PD-前置条件：用户未登录；极验服务正常；

###### 步骤一：调用人机校验接口

###### 步骤二：长时间不完成人机校验

###### 步骤三：等待校验超时

###### 步骤四：检查系统处理结果

###### ER-预期结果：1：系统检测到校验超时；2：提示用户重新进行校验；3：清除之前的校验状态；4：用户可重新开始流程；

### 兼容性测试

#### 多端适配验证

##### TL-PC端SSO登录人机校验验证

###### PD-前置条件：用户使用PC浏览器；极验服务正常；

###### 步骤一：在PC端访问SSO登录页面

###### 步骤二：进行人机校验

###### 步骤三：完成登录流程

###### ER-预期结果：1：PC端极验组件正常展示；2：交互体验良好；3：登录功能正常；

##### TL-H5端SSO登录人机校验验证

###### PD-前置条件：用户使用移动端浏览器；极验服务正常；

###### 步骤一：在H5端访问SSO登录页面

###### 步骤二：进行人机校验

###### 步骤三：完成登录流程

###### ER-预期结果：1：H5端极验组件适配良好；2：触摸交互正常；3：登录功能正常；

### 性能测试

#### 接口响应性能

##### TL-核身接口showAgreement参数响应时间验证

###### PD-前置条件：测试环境稳定；网络正常；

###### 步骤一：并发调用个人核身接口

###### 步骤二：设置showAgreement为true

###### 步骤三：记录接口响应时间

###### 步骤四：统计性能数据

###### ER-预期结果：1：接口响应时间小于2秒；2：并发处理能力正常；3：协议展示不影响性能；

##### TL-国际短信发送性能验证

###### PD-前置条件：测试环境稳定；短信服务正常；

###### 步骤一：批量发送国际短信

###### 步骤二：记录发送耗时

###### 步骤三：统计成功率

###### ER-预期结果：1：短信发送成功率大于95%；2：平均发送时间小于10秒；3：扣费计算准确；

### 安全测试

#### 参数安全验证

##### TL-showAgreement参数SQL注入安全验证

###### PD-前置条件：用户已登录；具有核身权限；

###### 步骤一：调用个人核身接口

###### 步骤二：在showAgreement参数中注入SQL语句

###### 步骤三：提交请求

###### 步骤四：检查系统响应

###### ER-预期结果：1：系统拒绝恶意参数；2：返回参数格式错误；3：不执行SQL注入；4：记录安全日志；

##### TL-极验参数篡改安全验证

###### PD-前置条件：用户未登录；极验服务正常；

###### 步骤一：获取正常的极验参数

###### 步骤二：篡改geetest_validate参数

###### 步骤三：调用发送验证码接口

###### 步骤四：检查系统响应

###### ER-预期结果：1：系统检测到参数篡改；2：校验失败；3：拒绝发送验证码；4：记录安全事件；

## 冒烟测试用例

### 需求1核心功能冒烟

#### MYTL-个人核身网页接口showAgreement基本功能验证

##### PD-前置条件：用户已登录；具有核身权限；协议服务正常；

##### 步骤一：调用个人核身网页接口

##### 步骤二：设置showAgreement参数为true

##### 步骤三：提交核身请求并检查协议展示

##### ER-预期结果：1：接口调用成功；2：协议正常展示；3：核身流程正常；

#### MYTL-showAgreement参数默认值功能验证

##### PD-前置条件：用户已登录；具有核身权限；

##### 步骤一：调用个人核身网页接口

##### 步骤二：不传showAgreement参数

##### 步骤三：检查协议展示状态

##### ER-预期结果：1：接口调用成功；2：默认不展示协议；3：功能正常；

### 需求2核心功能冒烟

#### MYTL-V3账号解绑微信小程序跳转基本功能验证

##### PD-前置条件：用户已在微信小程序中登录；账号已绑定；

##### 步骤一：调用V3账号解绑接口

##### 步骤二：设置redirectUrl为"wechat://back"

##### 步骤三：执行解绑并检查跳转

##### ER-预期结果：1：解绑成功；2：成功唤起微信back方法；3：跳转正常；

### 需求3核心功能冒烟

#### MYTL-国际短信扣费基本功能验证

##### PD-前置条件：用户账户余额充足；短信服务正常；

##### 步骤一：使用国际手机号发送短信

##### 步骤二：调用意愿认证短信接口

##### 步骤三：检查扣费记录

##### ER-预期结果：1：短信发送成功；2：按国际短信标准扣费；3：扣费准确；

#### MYTL-国内短信扣费基本功能验证

##### PD-前置条件：用户账户余额充足；短信服务正常；

##### 步骤一：使用国内手机号发送短信

##### 步骤二：调用意愿认证短信接口

##### 步骤三：检查扣费记录

##### ER-预期结果：1：短信发送成功；2：按国内短信标准扣费；3：扣费准确；

### 需求4核心功能冒烟

#### MYTL-企业法人刷脸加四要素认证基本功能验证

##### PD-前置条件：企业账户已注册；法人信息完整；

##### 步骤一：访问v2版本企业实名页面

##### 步骤二：进行法人刷脸认证

##### 步骤三：完成组织机构四要素验证

##### ER-预期结果：1：认证流程正常；2：认证方式正确显示；3：认证成功；

### 需求5核心功能冒烟

#### MYTL-SSO登录人机校验基本功能验证

##### PD-前置条件：用户未登录；极验服务正常；

##### 步骤一：调用人机校验接口

##### 步骤二：完成极验校验

##### 步骤三：发送验证码并完成登录

##### ER-预期结果：1：人机校验通过；2：验证码发送成功；3：登录成功；

#### MYTL-极验降级处理基本功能验证

##### PD-前置条件：用户未登录；极验服务异常；

##### 步骤一：访问SSO登录页面

##### 步骤二：检查降级处理

##### 步骤三：完成登录流程

##### ER-预期结果：1：自动启用降级模式；2：不展示极验组件；3：登录正常；

## 线上验证用例

### 需求1线上验证

#### PATL-个人核身接口showAgreement线上功能验证

##### PD-前置条件：生产环境；用户已登录；具有核身权限；

##### 步骤一：调用个人核身网页接口

##### 步骤二：设置showAgreement参数为true

##### 步骤三：验证协议展示和核身流程

##### ER-预期结果：1：接口正常响应；2：协议正确展示；3：核身功能正常；4：用户体验良好；

#### PATL-多个核身接口showAgreement参数一致性验证

##### PD-前置条件：生产环境；用户已登录；

##### 步骤一：分别调用四个核身接口

##### 步骤二：设置showAgreement参数为true

##### 步骤三：验证协议展示一致性

##### ER-预期结果：1：所有接口功能正常；2：协议展示一致；3：参数优先级正确；

### 需求2线上验证

#### PATL-V3账号解绑微信小程序跳转线上验证

##### PD-前置条件：生产环境；微信小程序；用户已绑定账号；

##### 步骤一：在微信小程序中执行解绑操作

##### 步骤二：设置redirectUrl为"wechat://back"

##### 步骤三：验证解绑和跳转功能

##### ER-预期结果：1：解绑操作成功；2：微信back方法正常唤起；3：用户体验流畅；

### 需求3线上验证

#### PATL-国际短信扣费功能线上验证

##### PD-前置条件：生产环境；客户账户正常；

##### 步骤一：使用国际手机号发送意愿认证短信

##### 步骤二：检查短信发送状态

##### 步骤三：验证扣费记录准确性

##### ER-预期结果：1：短信正常发送；2：扣费按国际标准执行；3：客户账单准确；

#### PATL-白名单客户国际短信扣费验证

##### PD-前置条件：生产环境；白名单客户；

##### 步骤一：白名单客户发送国际短信

##### 步骤二：检查扣费标准

##### 步骤三：验证白名单控制效果

##### ER-预期结果：1：短信正常发送；2：按普通短信扣费；3：白名单功能生效；

### 需求4线上验证

#### PATL-企业法人认证方式调整线上验证

##### PD-前置条件：生产环境；企业用户；

##### 步骤一：进行企业法人认证

##### 步骤二：完成刷脸和四要素验证

##### 步骤三：检查认证记录和出证信息

##### ER-预期结果：1：认证流程正常；2：认证方式正确记录；3：出证信息准确；

### 需求5线上验证

#### PATL-SSO登录人机校验线上验证

##### PD-前置条件：生产环境；用户未登录；

##### 步骤一：访问SSO登录页面

##### 步骤二：完成人机校验流程

##### 步骤三：验证登录功能

##### ER-预期结果：1：人机校验正常工作；2：登录流程顺畅；3：用户体验良好；

#### PATL-极验降级功能线上验证

##### PD-前置条件：生产环境；极验服务可能异常；

##### 步骤一：模拟极验服务异常场景

##### 步骤二：访问SSO登录页面

##### 步骤三：验证降级处理效果

##### ER-预期结果：1：系统自动降级；2：登录功能不受影响；3：用户无感知切换；
