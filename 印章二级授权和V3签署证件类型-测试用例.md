# 印章开放页面二级授权功能和V3api签署证件类型-测试用例

## 需求1：印章开放页面-支持印章二级授权功能

### 功能测试

#### 新增二级授权功能

##### TL-管理员用户新增指定企业成员二级授权成功验证
	PD-前置条件：用户具有管理员权限；存在有效的一级印章授权记录；企业内存在可授权成员；存在可选择的合同模板；
	步骤一：进入印章开放页面
	步骤二：点击"新增授权"按钮
	步骤三：在弹窗中选择授权对象为"指定企业成员"
	步骤四：选择具体的企业成员（多选）
	步骤五：选择授权角色为"印章使用员"
	步骤六：选择授权范围为指定模板并选择具体模板
	步骤七：设置有效期限在一级授权有效期范围内
	步骤八：点击提交按钮
	ER-预期结果：1：弹窗正常展示新增授权表单；2：授权关系字段置灰显示"企业内成员"；3：可正常选择企业成员和模板；4：提交成功并生成二级授权记录；5：页面刷新显示新增的授权记录；

##### TL-管理员用户新增全部企业成员二级授权成功验证
	PD-前置条件：用户具有管理员权限；存在有效的一级印章授权记录；企业内存在成员；存在可选择的合同模板；
	步骤一：进入印章开放页面
	步骤二：点击"新增授权"按钮
	步骤三：在弹窗中选择授权对象为"全部企业成员"
	步骤四：选择授权角色为"印章审批员"
	步骤五：选择授权范围为指定模板并选择具体模板
	步骤六：设置有效期限在一级授权有效期范围内
	步骤七：点击提交按钮
	ER-预期结果：1：弹窗正常展示；2：授权对象可选择全部企业成员；3：授权角色可选择印章审批员；4：提交成功生成授权记录；5：授权记录正确显示授权对象为全部成员；

##### TL-新增二级授权有效期超出一级授权范围失败验证
	PD-前置条件：用户具有管理员权限；存在有效的一级印章授权记录；一级授权有效期为2024-01-01至2024-12-31；
	步骤一：进入印章开放页面
	步骤二：点击"新增授权"按钮
	步骤三：填写授权表单基本信息
	步骤四：设置有效期限为2024-01-01至2025-01-31（超出一级授权范围）
	步骤五：点击提交按钮
	ER-预期结果：1：系统进行有效期校验；2：提交失败；3：显示错误提示"二级授权失效时间晚于了一级授权记录的失效时间"；4：表单不关闭，用户可重新修改；

##### TL-授权对象为全部成员时选择全部合同限制验证
	PD-前置条件：用户具有管理员权限；存在有效的一级印章授权记录；
	步骤一：进入印章开放页面
	步骤二：点击"新增授权"按钮
	步骤三：选择授权对象为"全部企业成员"
	步骤四：尝试选择授权范围为全部合同
	步骤五：点击提交按钮
	ER-预期结果：1：系统进行业务规则校验；2：提交失败；3：显示相应的错误提示；4：不允许全部成员授权给全部合同；

##### TL-一级授权为appId授权时不可新增二级授权验证
	PD-前置条件：用户具有管理员权限；存在一级授权记录且授权范围为appId；
	步骤一：进入印章开放页面
	步骤二：查看授权记录列表
	步骤三：尝试点击"新增授权"按钮
	ER-预期结果：1：系统检测到一级授权为appId授权；2：新增授权按钮不可点击或不显示；3：如果点击提交显示错误提示；

#### 查看二级授权功能

##### TL-查看二级授权详情页面展示验证
	PD-前置条件：存在二级授权记录；用户有查看权限；
	步骤一：进入印章开放页面
	步骤二：找到包含二级授权的一级授权记录
	步骤三：点击"查看授权"按钮
	步骤四：查看二级授权列表页面
	步骤五：检查列表展示字段和分页功能
	ER-预期结果：1：成功展开二级授权详情页面；2：正确显示授权对象、授权角色、授权范围、自动落章、有效期、接受审批通知、状态等字段；3：分页功能正常；4：根据不同状态显示对应操作按钮；

##### TL-不同授权状态下操作按钮展示验证
	PD-前置条件：存在不同状态的二级授权记录（即将过期、待授权、已失效、正常）；
	步骤一：进入二级授权详情页面
	步骤二：查看"即将过期"状态的授权记录
	步骤三：查看"待授权"状态的授权记录
	步骤四：查看"已失效"状态的授权记录
	步骤五：查看"正常"状态的授权记录
	ER-预期结果：1：即将过期状态显示：查看、重新授权、下载授权书、删除按钮；2：待授权状态显示：查看、重新授权、删除按钮；3：已失效状态显示：查看、重新授权、下载授权书、删除按钮；4：正常状态显示对应操作按钮；

#### 重新授权功能

##### TL-失效状态授权记录重新授权成功验证
	PD-前置条件：存在状态为"失效"的二级授权记录；当前印章为有效章；授权记录不是管理员默认授权；用户有管理员权限；
	步骤一：进入二级授权详情页面
	步骤二：找到失效状态的授权记录
	步骤三：点击"重新授权"按钮
	步骤四：在弹窗中查看授权信息（仅展示不可修改）
	步骤五：修改授权有效期在一级授权范围内
	步骤六：点击提交按钮
	ER-预期结果：1：重新授权弹窗正常展示；2：除有效期外其他信息仅做展示；3：有效期可正常修改；4：提交成功生成新的授权记录；5：跳转至授权书签署页面；

##### TL-重新授权有效期超出一级授权范围失败验证
	PD-前置条件：存在失效的二级授权记录；一级授权有效期有限制；用户有管理员权限；
	步骤一：进入二级授权详情页面
	步骤二：点击失效记录的"重新授权"按钮
	步骤三：修改有效期超出一级授权有效期范围
	步骤四：点击提交按钮
	ER-预期结果：1：系统进行有效期校验；2：提交失败；3：显示错误提示"二级授权失效时间晚于了一级授权记录的失效时间"；4：弹窗不关闭，可重新修改；

#### 权限控制功能

##### TL-非管理员用户权限限制验证
	PD-前置条件：当前操作用户没有管理员权限；存在二级授权记录；
	步骤一：进入印章开放页面
	步骤二：查看页面操作按钮
	步骤三：尝试进行需要管理员权限的操作
	ER-预期结果：1：新增授权按钮不显示或不可点击；2：重新授权按钮不显示或不可点击；3：删除授权按钮不显示或不可点击；4：只能进行查看操作；

##### TL-管理员默认授权记录操作限制验证
	PD-前置条件：存在管理员的默认授权记录；用户有管理员权限；
	步骤一：进入二级授权详情页面
	步骤二：找到管理员默认授权记录
	步骤三：查看该记录的操作按钮
	ER-预期结果：1：管理员默认授权记录不显示重新授权按钮；2：不显示删除按钮；3：只显示查看和下载授权书按钮（如果存在授权书）；

### 边界测试

#### 有效期边界测试

##### TL-二级授权有效期等于一级授权有效期边界验证
	PD-前置条件：一级授权有效期为2024-01-01至2024-12-31；用户有管理员权限；
	步骤一：新增二级授权
	步骤二：设置有效期为2024-01-01至2024-12-31（与一级授权完全相同）
	步骤三：提交表单
	ER-预期结果：1：系统接受该有效期设置；2：提交成功；3：生成正确的授权记录；

##### TL-二级授权有效期晚一天超出边界验证
	PD-前置条件：一级授权有效期为2024-01-01至2024-12-31；用户有管理员权限；
	步骤一：新增二级授权
	步骤二：设置有效期为2024-01-01至2025-01-01（晚一天）
	步骤三：提交表单
	ER-预期结果：1：系统检测到有效期超出范围；2：提交失败；3：显示错误提示；

### 异常测试

#### 网络异常测试

##### TL-新增授权提交时网络中断异常处理验证
	PD-前置条件：用户有管理员权限；网络环境可控制；
	步骤一：进入新增授权页面
	步骤二：填写完整的授权信息
	步骤三：断开网络连接
	步骤四：点击提交按钮
	步骤五：恢复网络连接
	ER-预期结果：1：系统检测到网络异常；2：显示网络错误提示；3：表单数据保持不丢失；4：网络恢复后可重新提交；

#### 数据异常测试

##### TL-企业成员数据异常时授权对象选择验证
	PD-前置条件：企业成员数据异常或为空；用户有管理员权限；
	步骤一：进入新增授权页面
	步骤二：选择授权对象为"指定企业成员"
	步骤三：尝试选择企业成员
	ER-预期结果：1：系统检测到成员数据异常；2：显示相应的错误提示或空状态；3：不允许提交表单；4：提供重新加载或刷新功能；

## 需求2：【V3api】签署接口指定实名认证的证件类型

### 功能测试

#### 证件类型处理逻辑

##### TL-创建签署流程时psnIDCardNum为空psnIDCardType不为空透传验证
	PD-前置条件：具有创建签署流程权限；
	步骤一：调用V3创建签署流程接口
	步骤二：设置psnIDCardNum为空
	步骤三：设置psnIDCardType为CRED_PSN_CH_TWCARD
	步骤四：提交创建请求
	步骤五：进入实名认证页面
	ER-预期结果：1：签署流程创建成功；2：实名认证页面展示证件类型为"台湾来往大陆通行证"；3：证件类型字段不可修改；4：certType成功透传到实名认证系统；

##### TL-创建签署流程时psnIDCardNum不为空psnIDCardType为空默认值设置验证
	PD-前置条件：用户已登录；具有创建签署流程权限；实名认证服务正常；
	步骤一：调用V3创建签署流程接口
	步骤二：设置psnIDCardNum为有效身份证号
	步骤三：设置psnIDCardType为空
	步骤四：提交创建请求
	步骤五：检查系统处理结果
	ER-预期结果：1：签署流程创建成功；2：psnIDCardType自动设置为CRED_PSN_CH_IDCARD；3：实名认证页面展示证件类型为"中华人民共和国居民身份证"；4：证件类型字段不可修改；

##### TL-创建签署流程时psnIDCardNum和psnIDCardType都不为空正常处理验证
	PD-前置条件：具有创建签署流程权限；实名认证服务正常；
	步骤一：调用V3创建签署流程接口
	步骤二：设置psnIDCardNum为有效身份证号
	步骤三：设置psnIDCardType为CRED_PSN_CH_IDCARD
	步骤四：提交创建请求
	步骤五：进入实名认证页面
	ER-预期结果：1：签署流程创建成功；2：实名认证页面正确展示对应证件类型；3：证件号码和证件类型匹配；4：实名认证流程正常进行；

#### 批量签署兼容性

##### TL-批量签署场景下相同签署人不同certType冲突验证
	PD-前置条件：存在签署人甲；创建流程A时certType为空；创建流程B时certType不为空；
	步骤一：创建签署流程A，签署人甲，certType为空
	步骤二：创建签署流程B，签署人甲，certType为CRED_PSN_CH_TWCARD
	步骤三：尝试将流程A和流程B加入批量签署
	步骤四：执行批量签署操作
	ER-预期结果：1：系统检测到同一签署人的certType不一致；2：判定为不是同一个签署人；3：不能执行批量签署；4：显示相应的错误提示；

##### TL-批量签署场景下相同签署人相同certType正常验证
	PD-前置条件：存在签署人甲；多个签署流程的certType一致；
	步骤一：创建多个签署流程，签署人都是甲，certType都为CRED_PSN_CH_IDCARD
	步骤二：将这些流程加入批量签署
	步骤三：执行批量签署操作
	ER-预期结果：1：系统识别为同一签署人；2：批量签署功能正常执行；3：实名认证页面正确展示证件类型；4：签署流程正常完成；

### 边界测试

#### 证件类型边界测试

##### TL-支持的证件类型边界值验证
	PD-前置条件：具有创建签署流程权限；
	步骤一：分别使用所有支持的证件类型创建签署流程
	步骤二：验证CRED_PSN_CH_IDCARD类型
	步骤三：验证CRED_PSN_CH_TWCARD类型
	步骤四：验证其他支持的证件类型
	ER-预期结果：1：所有支持的证件类型都能正常处理；2：实名页面正确展示对应证件类型名称；3：证件类型字段不可修改；

##### TL-不支持的证件类型异常处理验证
	PD-前置条件：具有创建签署流程权限；
	步骤一：调用V3创建签署流程接口
	步骤二：设置psnIDCardType为不支持的证件类型
	步骤三：提交创建请求
	ER-预期结果：1：系统检测到不支持的证件类型；2：返回相应的错误信息；3：签署流程创建失败；4：提供正确的证件类型列表提示；

### 异常测试

#### 接口异常测试

##### TL-实名认证服务异常时证件类型处理验证
	PD-前置条件：实名认证服务异常或不可用；
	步骤一：调用V3创建签署流程接口
	步骤二：设置psnIDCardType为CRED_PSN_CH_TWCARD
	步骤三：提交创建请求
	步骤四：尝试进入实名认证页面
	ER-预期结果：1：签署流程创建成功；2：证件类型信息正确保存；3：实名认证服务恢复后能正确展示证件类型；4：显示服务异常提示；

#### 数据异常测试

##### TL-证件类型数据格式异常处理验证
	PD-前置条件：具有创建签署流程权限；
	步骤一：调用V3创建签署流程接口
	步骤二：设置psnIDCardType为格式错误的值
	步骤三：提交创建请求
	ER-预期结果：1：系统进行数据格式校验；2：返回格式错误提示；3：签署流程创建失败；4：提供正确的格式说明；

### 性能测试

#### 响应时间测试

##### TL-新增二级授权接口响应时间验证
	PD-前置条件：用户有管理员权限；系统负载正常；
	步骤一：记录开始时间
	步骤二：调用新增二级授权接口
	步骤三：记录结束时间
	步骤四：计算响应时间
	ER-预期结果：1：接口响应时间小于3秒；2：操作流畅无卡顿；3：页面刷新及时；

##### TL-V3签署流程创建接口响应时间验证
	PD-前置条件：具有创建签署流程权限；网络环境良好；
	步骤一：记录开始时间
	步骤二：调用V3创建签署流程接口
	步骤三：记录结束时间
	步骤四：计算响应时间
	ER-预期结果：1：接口响应时间小于2秒；2：签署流程创建成功；3：返回数据完整；

#### 并发性能测试

##### TL-多用户同时新增二级授权并发验证
	PD-前置条件：多个管理员用户；系统支持并发操作；
	步骤一：准备10个管理员用户
	步骤二：同时进行新增二级授权操作
	步骤三：检查操作结果
	步骤四：验证数据一致性
	ER-预期结果：1：所有用户操作都能成功；2：数据无冲突；3：系统性能稳定；4：响应时间在可接受范围内；

### 安全测试

#### 权限验证测试

##### TL-越权访问二级授权管理功能验证
	PD-前置条件：普通用户账号；存在二级授权管理功能；
	步骤一：使用普通用户登录
	步骤二：尝试直接访问新增授权接口
	步骤三：尝试访问删除授权接口
	步骤四：检查系统响应
	ER-预期结果：1：系统拒绝越权访问；2：返回权限不足错误；3：不允许执行敏感操作；4：记录安全日志；

##### TL-V3签署接口参数篡改安全验证
	PD-前置条件：具有签署权限；接口调用环境；
	步骤一：正常调用V3签署接口
	步骤二：尝试篡改psnIDCardType参数
	步骤三：提交篡改后的请求
	步骤四：检查系统响应
	ER-预期结果：1：系统检测到参数异常；2：拒绝处理篡改请求；3：返回安全错误提示；4：记录安全事件；

### 兼容性测试

#### 多端验证测试

##### TL-印章开放页面多浏览器兼容性验证
	PD-前置条件：准备Chrome、Firefox、Safari、Edge浏览器；
	步骤一：在Chrome浏览器中测试二级授权功能
	步骤二：在Firefox浏览器中测试相同功能
	步骤三：在Safari浏览器中测试相同功能
	步骤四：在Edge浏览器中测试相同功能
	ER-预期结果：1：所有浏览器都能正常显示页面；2：功能操作无异常；3：样式显示一致；4：交互体验良好；

##### TL-V3签署接口多设备兼容性验证
	PD-前置条件：PC端、移动端H5、微信小程序环境；
	步骤一：在PC端调用V3签署接口
	步骤二：在移动端H5调用相同接口
	步骤三：在微信小程序中调用接口
	步骤四：对比各端处理结果
	ER-预期结果：1：各端接口调用都成功；2：证件类型处理逻辑一致；3：实名认证页面正常展示；4：用户体验良好；

## 冒烟测试用例

### 印章二级授权核心功能

#### MYTL-管理员新增指定企业成员二级授权基本流程验证
	PD-前置条件：用户具有管理员权限；存在有效的一级印章授权记录；
	步骤一：进入印章开放页面
	步骤二：点击"新增授权"按钮
	步骤三：选择授权对象为"指定企业成员"并选择成员
	步骤四：选择授权角色和范围
	步骤五：设置有效期并提交
	ER-预期结果：1：新增授权功能正常；2：授权记录创建成功；3：页面正常刷新显示；

#### MYTL-查看二级授权详情基本功能验证
	PD-前置条件：存在二级授权记录；
	步骤一：进入印章开放页面
	步骤二：点击"查看授权"按钮
	步骤三：查看二级授权列表
	ER-预期结果：1：详情页面正常展示；2：授权信息显示完整；3：操作按钮正确显示；

#### MYTL-非管理员用户权限控制基本验证
	PD-前置条件：当前用户没有管理员权限；
	步骤一：进入印章开放页面
	步骤二：查看页面操作按钮
	ER-预期结果：1：敏感操作按钮不显示；2：只能进行查看操作；3：权限控制正确；

### V3签署证件类型核心功能

#### MYTL-证件类型透传基本功能验证
	PD-前置条件：具有创建签署流程权限；
	步骤一：调用V3创建签署流程接口
	步骤二：设置psnIDCardNum为空，psnIDCardType为CRED_PSN_CH_TWCARD
	步骤三：进入实名认证页面
	ER-预期结果：1：签署流程创建成功；2：证件类型正确透传；3：实名页面正确展示；

#### MYTL-证件类型默认值设置基本验证
	PD-前置条件：具有创建签署流程权限；
	步骤一：调用V3创建签署流程接口
	步骤二：设置psnIDCardNum不为空，psnIDCardType为空
	步骤三：检查系统处理结果
	ER-预期结果：1：psnIDCardType自动设置为CRED_PSN_CH_IDCARD；2：默认值处理正确；

#### MYTL-批量签署兼容性基本验证
	PD-前置条件：存在相同签署人的多个流程；
	步骤一：创建多个签署流程，certType一致
	步骤二：执行批量签署操作
	ER-预期结果：1：批量签署功能正常；2：签署人识别正确；3：流程执行成功；

## 线上验证用例

### 印章二级授权线上验证

#### PATL-生产环境新增二级授权完整流程验证
	PD-前置条件：生产环境；真实管理员账号；有效一级授权；
	步骤一：登录生产环境印章开放页面
	步骤二：执行新增二级授权完整流程
	步骤三：验证授权记录生成
	步骤四：验证权限生效
	ER-预期结果：1：生产环境功能正常；2：授权流程完整；3：数据准确无误；4：业务功能可用；

#### PATL-生产环境权限控制验证
	PD-前置条件：生产环境；不同权限级别用户；
	步骤一：使用普通用户访问印章开放页面
	步骤二：验证权限控制效果
	步骤三：使用管理员用户验证功能可用性
	ER-预期结果：1：权限控制准确；2：普通用户无法执行敏感操作；3：管理员功能正常；

### V3签署证件类型线上验证

#### PATL-生产环境证件类型透传完整验证
	PD-前置条件：生产环境；真实签署流程；实名认证服务；
	步骤一：在生产环境创建签署流程
	步骤二：设置不同证件类型参数
	步骤三：执行完整签署流程
	步骤四：验证实名认证页面展示
	ER-预期结果：1：证件类型正确透传；2：实名页面正确展示；3：签署流程正常完成；4：用户体验良好；

#### PATL-生产环境批量签署兼容性验证
	PD-前置条件：生产环境；真实用户；多个签署流程；
	步骤一：创建多个包含证件类型的签署流程
	步骤二：执行批量签署操作
	步骤三：验证签署人识别逻辑
	步骤四：完成批量签署流程
	ER-预期结果：1：批量签署功能正常；2：证件类型兼容性良好；3：业务流程完整；4：数据处理准确；

#### PATL-生产环境多端兼容性验证
	PD-前置条件：生产环境；PC端、H5端、小程序端；
	步骤一：在PC端测试V3签署接口
	步骤二：在H5端测试相同功能
	步骤三：在小程序端测试功能
	步骤四：对比各端表现
	ER-预期结果：1：各端功能一致；2：证件类型处理统一；3：用户体验良好；4：兼容性完好；
