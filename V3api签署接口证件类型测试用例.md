# 【V3api】签署接口指定实名认证的证件类型 - 测试用例

## 功能测试

### 1. 基础参数组合功能

#### 1.1 psnIDCardNum不为空，psnIDCardType为空（默认值设置）
- **前置条件**
  - V3签署接口可正常访问
  - 准备有效的身份证号码
- **操作步骤**
  1. 调用V3创建签署流程接口
  2. 传入psnIDCardNum="110101199001011234"
  3. psnIDCardType参数不传或传空值
  4. 提交请求
- **预期结果**
  - 接口调用成功
  - psnIDCardType自动设置为"CRED_PSN_CH_IDCARD"
  - 返回成功响应

#### 1.2 psnIDCardNum为空，psnIDCardType不为空（透传场景）
- **前置条件**
  - V3签署接口可正常访问
  - 准备有效的证件类型参数
- **操作步骤**
  1. 调用V3创建签署流程接口
  2. psnIDCardNum参数不传或传空值
  3. 传入psnIDCardType="CRED_PSN_CH_TWCARD"
  4. 提交请求
- **预期结果**
  - 接口调用成功
  - psnIDCardType值保持为"CRED_PSN_CH_TWCARD"
  - 返回成功响应

#### 1.3 psnIDCardNum不为空，psnIDCardType不为空（完整参数）
- **前置条件**
  - V3签署接口可正常访问
  - 准备有效的身份证号码和证件类型
- **操作步骤**
  1. 调用V3创建签署流程接口
  2. 传入psnIDCardNum="110101199001011234"
  3. 传入psnIDCardType="CRED_PSN_CH_IDCARD"
  4. 提交请求
- **预期结果**
  - 接口调用成功
  - 参数值保持不变
  - 返回成功响应

### 2. 证件类型支持功能

#### 2.1 身份证类型支持
- **前置条件**
  - V3签署接口可正常访问
- **操作步骤**
  1. 调用V3创建签署流程接口
  2. 传入psnIDCardType="CRED_PSN_CH_IDCARD"
  3. 提交请求
- **预期结果**
  - 接口调用成功
  - 证件类型被正确识别和处理

#### 2.2 台湾来往大陆通行证支持
- **前置条件**
  - V3签署接口可正常访问
- **操作步骤**
  1. 调用V3创建签署流程接口
  2. 传入psnIDCardType="CRED_PSN_CH_TWCARD"
  3. 提交请求
- **预期结果**
  - 接口调用成功
  - 证件类型被正确识别和处理

### 3. 实名认证透传功能

#### 3.1 证件类型透传到实名页面
- **前置条件**
  - V3签署流程已创建
  - psnIDCardNum为空，psnIDCardType="CRED_PSN_CH_TWCARD"
- **操作步骤**
  1. 进入签署流程
  2. 触发实名认证流程
  3. 查看实名认证页面
- **预期结果**
  - 实名页面显示证件类型为"台湾来往大陆通行证"
  - 证件类型字段不可修改
  - 用户只能输入对应证件号码

#### 3.2 实名认证成功完成
- **前置条件**
  - 实名页面已正确显示证件类型
  - 准备匹配的证件号码
- **操作步骤**
  1. 在实名页面输入对应证件号码
  2. 完成其他必要的实名认证步骤
  3. 提交实名认证
- **预期结果**
  - 实名认证成功
  - 可以继续后续签署流程

### 4. 批量签署功能

#### 4.1 同一签署人certType一致的批量签署
- **前置条件**
  - 创建多个签署流程A、B
  - 同一签署人在流程A和B中certType都为"CRED_PSN_CH_IDCARD"
- **操作步骤**
  1. 选择流程A和B进行批量签署
  2. 提交批量签署请求
- **预期结果**
  - 系统识别为同一签署人
  - 批量签署成功执行

#### 4.2 同一签署人certType不一致的批量签署阻止
- **前置条件**
  - 创建多个签署流程A、B
  - 同一签署人在流程A中certType为空，流程B中certType为"CRED_PSN_CH_TWCARD"
- **操作步骤**
  1. 选择流程A和B进行批量签署
  2. 提交批量签署请求
- **预期结果**
  - 系统判定为不同签署人
  - 批量签署被阻止
  - 返回相应错误提示

## 异常测试

### 5. 参数异常测试

#### 5.1 psnIDCardNum和psnIDCardType都为空
- **前置条件**
  - V3签署接口可正常访问
- **操作步骤**
  1. 调用V3创建签署流程接口
  2. psnIDCardNum和psnIDCardType都不传或传空值
  3. 提交请求
- **预期结果**
  - 接口调用成功或返回明确的业务规则说明
  - 系统按既定规则处理

#### 5.2 不支持的证件类型
- **前置条件**
  - V3签署接口可正常访问
- **操作步骤**
  1. 调用V3创建签署流程接口
  2. 传入psnIDCardType="INVALID_CERT_TYPE"
  3. 提交请求
- **预期结果**
  - 接口返回参数错误
  - 错误信息明确指出不支持的证件类型

#### 5.3 身份证号码格式错误
- **前置条件**
  - V3签署接口可正常访问
- **操作步骤**
  1. 调用V3创建签署流程接口
  2. 传入格式错误的psnIDCardNum="123456"
  3. 提交请求
- **预期结果**
  - 接口返回参数格式错误
  - 错误信息明确指出身份证号码格式不正确

### 6. 实名认证异常测试

#### 6.1 证件类型与证件号码不匹配
- **前置条件**
  - 签署流程中psnIDCardType="CRED_PSN_CH_TWCARD"
  - 进入实名认证页面
- **操作步骤**
  1. 在实名页面输入身份证号码格式的证件号
  2. 提交实名认证
- **预期结果**
  - 实名认证失败
  - 返回证件类型与号码不匹配的错误提示

## 边界测试

### 7. 参数边界测试

#### 7.1 身份证号码长度边界
- **前置条件**
  - V3签署接口可正常访问
- **操作步骤**
  1. 测试17位身份证号码
  2. 测试19位身份证号码
  3. 测试标准18位身份证号码
- **预期结果**
  - 18位号码正常处理
  - 非18位号码返回格式错误

#### 7.2 证件类型字段长度边界
- **前置条件**
  - V3签署接口可正常访问
- **操作步骤**
  1. 传入超长的certType值
  2. 传入空字符串的certType值
- **预期结果**
  - 超长值被截断或返回错误
  - 空字符串按空值处理

### 8. 兼容性测试

#### 8.1 老版本接口兼容性
- **前置条件**
  - 存在使用老版本接口的客户端
- **操作步骤**
  1. 使用老版本接口调用方式
  2. 不传入certType参数
  3. 验证功能正常性
- **预期结果**
  - 老版本调用方式仍然有效
  - 新功能不影响原有逻辑

#### 8.2 不同客户端版本兼容性
- **前置条件**
  - 准备不同版本的客户端
- **操作步骤**
  1. 分别使用不同版本客户端调用接口
  2. 验证新功能的支持情况
- **预期结果**
  - 新版本客户端支持所有新功能
  - 老版本客户端保持原有功能不受影响

## 性能测试

### 9. 接口性能测试

#### 9.1 单接口响应时间测试
- **前置条件**
  - V3签署接口可正常访问
  - 准备性能测试工具
- **操作步骤**
  1. 发送标准的创建签署流程请求
  2. 记录接口响应时间
  3. 重复测试100次
- **预期结果**
  - 平均响应时间≤2秒
  - 95%请求响应时间≤3秒

#### 9.2 并发请求性能测试
- **前置条件**
  - V3签署接口可正常访问
  - 准备并发测试工具
- **操作步骤**
  1. 同时发送100个创建签署流程请求
  2. 每个请求包含不同的certType参数
  3. 记录成功率和响应时间
- **预期结果**
  - 请求成功率≥99%
  - 平均响应时间≤5秒

### 10. 批量签署性能测试

#### 10.1 大批量签署处理性能
- **前置条件**
  - 创建100个签署流程
  - 所有流程使用相同签署人和certType
- **操作步骤**
  1. 执行批量签署操作
  2. 记录处理时间和成功率
- **预期结果**
  - 批量处理成功率≥99%
  - 处理时间在可接受范围内

## 安全测试

### 11. 参数安全测试

#### 11.1 SQL注入测试
- **前置条件**
  - V3签署接口可正常访问
- **操作步骤**
  1. 在psnIDCardNum中注入SQL语句
  2. 在psnIDCardType中注入SQL语句
  3. 提交请求
- **预期结果**
  - 接口正确处理恶意输入
  - 不发生SQL注入攻击
  - 返回参数格式错误

#### 11.2 XSS攻击测试
- **前置条件**
  - V3签署接口可正常访问
- **操作步骤**
  1. 在参数中注入JavaScript脚本
  2. 提交请求并查看响应
- **预期结果**
  - 脚本被正确转义或过滤
  - 不发生XSS攻击

#### 11.3 参数篡改测试
- **前置条件**
  - 正常的签署流程请求
- **操作步骤**
  1. 拦截并修改请求中的certType参数
  2. 尝试提交篡改后的请求
- **预期结果**
  - 系统检测到参数篡改
  - 拒绝处理篡改请求

## 用户体验测试

### 12. 实名认证用户体验测试

#### 12.1 证件类型显示准确性测试
- **前置条件**
  - 签署流程中设置psnIDCardType="CRED_PSN_CH_TWCARD"
- **操作步骤**
  1. 进入实名认证页面
  2. 查看证件类型显示
  3. 验证显示文本的准确性
- **预期结果**
  - 显示"台湾来往大陆通行证"而非代码
  - 文本显示清晰易懂

#### 12.2 证件类型不可修改提示测试
- **前置条件**
  - 实名认证页面已显示指定证件类型
- **操作步骤**
  1. 尝试点击或修改证件类型字段
  2. 查看是否有相应提示
- **预期结果**
  - 证件类型字段确实不可修改
  - 有明确的提示说明为什么不可修改

#### 12.3 错误提示友好性测试
- **前置条件**
  - 实名认证页面显示台湾通行证类型
- **操作步骤**
  1. 输入身份证格式的号码
  2. 提交认证
  3. 查看错误提示信息
- **预期结果**
  - 错误提示清晰明确
  - 提示用户应该输入正确格式的台湾通行证号码

## 业务流程测试

### 13. 完整业务流程测试

#### 13.1 标准签署流程测试
- **前置条件**
  - 准备完整的签署文档和签署人信息
- **操作步骤**
  1. 创建签署流程，指定certType="CRED_PSN_CH_TWCARD"
  2. 发送签署邀请
  3. 签署人进入签署页面
  4. 完成实名认证（证件类型已指定）
  5. 完成电子签署
- **预期结果**
  - 整个流程顺利完成
  - 实名认证环节证件类型正确显示且不可修改
  - 签署成功

#### 13.2 多人签署流程测试
- **前置条件**
  - 准备多个签署人，每人指定不同certType
- **操作步骤**
  1. 创建包含多个签署人的签署流程
  2. 为不同签署人设置不同的certType
  3. 各签署人依次完成签署
- **预期结果**
  - 每个签署人看到的证件类型与设置一致
  - 所有签署人都能成功完成签署

#### 13.3 批量签署完整流程测试
- **前置条件**
  - 同一签署人有多个待签署流程
  - 所有流程的certType设置一致
- **操作步骤**
  1. 签署人登录系统
  2. 选择多个待签署流程
  3. 执行批量签署
  4. 完成统一的实名认证
  5. 批量完成签署
- **预期结果**
  - 系统正确识别为同一签署人
  - 批量签署功能正常工作
  - 只需要进行一次实名认证

### 14. 异常恢复测试

#### 14.1 实名认证中断恢复测试
- **前置条件**
  - 签署流程进行到实名认证环节
  - 证件类型已透传显示
- **操作步骤**
  1. 在实名认证过程中关闭浏览器
  2. 重新打开并进入签署流程
  3. 查看实名认证页面状态
- **预期结果**
  - 证件类型设置得到保持
  - 可以继续完成实名认证

#### 14.2 批量签署中断恢复测试
- **前置条件**
  - 批量签署操作进行中
- **操作步骤**
  1. 在批量签署过程中发生网络中断
  2. 网络恢复后重新尝试批量签署
- **预期结果**
  - 系统能够正确恢复批量签署状态
  - 已完成的签署不会重复执行
