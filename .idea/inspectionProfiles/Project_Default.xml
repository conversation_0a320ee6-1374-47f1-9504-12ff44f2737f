<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="23">
            <item index="0" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="1" class="java.lang.String" itemvalue="rocketmq" />
            <item index="2" class="java.lang.String" itemvalue="flask_paginate" />
            <item index="3" class="java.lang.String" itemvalue="flasgger" />
            <item index="4" class="java.lang.String" itemvalue="rocketmq-client-python" />
            <item index="5" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="6" class="java.lang.String" itemvalue="xlwt" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
            <item index="8" class="java.lang.String" itemvalue="requests" />
            <item index="9" class="java.lang.String" itemvalue="requests_toolbelt" />
            <item index="10" class="java.lang.String" itemvalue="pandas" />
            <item index="11" class="java.lang.String" itemvalue="chinese_calendar" />
            <item index="12" class="java.lang.String" itemvalue="jmespath" />
            <item index="13" class="java.lang.String" itemvalue="Flask_SQLAlchemy" />
            <item index="14" class="java.lang.String" itemvalue="selenium" />
            <item index="15" class="java.lang.String" itemvalue="chinesecalendar" />
            <item index="16" class="java.lang.String" itemvalue="Flask_Cors" />
            <item index="17" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="18" class="java.lang.String" itemvalue="uwsgi" />
            <item index="19" class="java.lang.String" itemvalue="pypinyin" />
            <item index="20" class="java.lang.String" itemvalue="Flask" />
            <item index="21" class="java.lang.String" itemvalue="httprunner" />
            <item index="22" class="java.lang.String" itemvalue="jira" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N806" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>