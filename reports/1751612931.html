<head>
  <meta content="text/html; charset=utf-8" http-equiv="content-type" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> - TestReport</title>
  <style>
    body {
      background-color: #f2f2f2;
      color: #333;
      margin: 0 auto;
      width: 960px;
    }
    #summary {
      width: 960px;
      margin-bottom: 20px;
    }
    #summary th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    #summary td {
      background-color: lightblue;
      text-align: center;
      padding: 4px 8px;
    }
    .details {
      width: 960px;
      margin-bottom: 20px;
    }
    .details th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    .details tr .passed {
      background-color: lightgreen;
    }
    .details tr .failed {
      background-color: red;
    }
    .details tr .unchecked {
      background-color: gray;
    }
    .details td {
      background-color: lightblue;
      padding: 5px 12px;
    }
    .details .detail {
      background-color: lightgrey;
      font-size: smaller;
      padding: 5px 10px;
      line-height: 20px;
      text-align: left;
    }
    .details .success {
      background-color: greenyellow;
    }
    .details .error {
      background-color: red;
    }
    .details .failure {
      background-color: salmon;
    }
    .details .skipped {
      background-color: gray;
    }

    .button {
      font-size: 1em;
      padding: 6px;
      width: 4em;
      text-align: center;
      background-color: #06d85f;
      border-radius: 20px/50px;
      cursor: pointer;
      transition: all 0.3s ease-out;
    }
    a.button{
      color: gray;
      text-decoration: none;
      display: inline-block;
    }
    .button:hover {
      background: #2cffbd;
    }

    .overlay {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      transition: opacity 500ms;
      visibility: hidden;
      opacity: 0;
      line-height: 25px;
    }
    .overlay:target {
      visibility: visible;
      opacity: 1;
    }

    .popup {
      margin: 70px auto;
      padding: 20px;
      background: #fff;
      border-radius: 10px;
      width: 50%;
      position: relative;
      transition: all 3s ease-in-out;
    }

    .popup h2 {
      margin-top: 0;
      color: #333;
      font-family: Tahoma, Arial, sans-serif;
    }
    .popup .close {
      position: absolute;
      top: 20px;
      right: 30px;
      transition: all 200ms;
      font-size: 30px;
      font-weight: bold;
      text-decoration: none;
      color: #333;
    }
    .popup .close:hover {
      color: #06d85f;
    }
    .popup .content {
      max-height: 80%;
      overflow: auto;
      text-align: left;
    }
    .popup .separator {
      color:royalblue
    }

    @media screen and (max-width: 700px) {
      .box {
        width: 70%;
      }
      .popup {
        width: 70%;
      }
    }

  </style>
</head>

<body>
  <h1>Test Report: </h1>

  <h2>Summary</h2>
  <table id="summary">
    <tr>
      <th>START AT</th>
      <td colspan="4">2025-07-04 15:08:51</td>
    </tr>
    <tr>
      <th>DURATION</th>
      <td colspan="4">0.908 seconds</td>
    </tr>
    <tr>
      <th>PLATFORM</th>
      <td>HttpRunner 2.2.4 </td>
      <td>CPython 3.8.9 </td>
      <td colspan="2">macOS-12.5.1-x86_64-i386-64bit</td>
    </tr>
    <tr>
      <th>STAT</th>
      <th colspan="2">TESTCASES (success/fail)</th>
      <th colspan="2">TESTSTEPS (success/fail/error/skip)</th>
    </tr>
    <tr>
      <td>total (details) =></td>
      <td colspan="2">1 (0/1)</td>
      <td colspan="2">7 (6/1/0/0)</td>
    </tr>
  </table>

  <h2>Details</h2>

  
  
  <h3>签署文件下载</h3>
  <table id="suite_1" class="details">
    <tr>
      <td>TOTAL: 7</td>
      <td>SUCCESS: 6</td>
      <td>FAILED: 1</td>
      <td>ERROR: 0</td>
      <td>SKIPPED: 0</td>
    </tr>
    <tr>
      <th>Status</th>
      <th colspan="2">Name</th>
      <th>Response Time</th>
      <th>Detail</th>
    </tr>

    
    
    
    <tr id="record_1_1">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">签署文件下载</td>
      <td style="text-align:center;width:6em;">235.02 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_1_1">log-1</a>
        <div id="popup_log_1_1_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_1_1">&times;</a>

            <div class="content">
              <h3>Name: 签署文件下载</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.31.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 3438757422
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Service-Group</strong>: DEFAULT
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 184
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;operatorOid&#34;: &#34;72526616536d40b9864c79cd1954dbd1&#34;, &#34;orgOid&#34;: &#34;421612cd9edc4f859ddc0808d5a7e321&#34;, &#34;taskId&#34;: &#34;3a805a461c3346a99de127a6cd395f24&#34;, &#34;thirdOperatorId&#34;: &#34;&#34;, &#34;thirdOrgId&#34;: &#34;&#34;}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Fri, 04 Jul 2025 07:08:52 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 159
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T293T17516129321051513
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;flowTemplateName&#39;: &#39;&#39;, &#39;docs&#39;: [{&#39;totalPage&#39;: 1, &#39;source&#39;: 0, &#39;fileId&#39;: &#39;ecaa9ad9490842aaacaae5b5e9f6a66e&#39;, &#39;fileName&#39;: &#39;模板.pdf&#39;, &#39;fileUrl&#39;: &#39;https://esignoss.esign.cn/1111563786/99ac6dc1-10a4-43f1-ac19-08aaac39aea7/%E6%A8%A1%E6%9D%BF.pdf?Expires=1751616532&amp;OSSAccessKeyId=LTAI5tHULnSmVesom4Uy5ENh&amp;Signature=p7IXhME78F02Tkif6szYyIVKelQ%3D&#39;}]}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>373</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>235.02</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>227.016</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_2">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">签署文件下载operatorOid为空</td>
      <td style="text-align:center;width:6em;">95.40 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_2_1">log-1</a>
        <div id="popup_log_1_2_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_2_1">&times;</a>

            <div class="content">
              <h3>Name: 签署文件下载operatorOid为空</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.31.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 3438757422
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Service-Group</strong>: DEFAULT
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 184
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;operatorOid&#34;: &#34;72526616536d40b9864c79cd1954dbd1&#34;, &#34;orgOid&#34;: &#34;421612cd9edc4f859ddc0808d5a7e321&#34;, &#34;taskId&#34;: &#34;3a805a461c3346a99de127a6cd395f24&#34;, &#34;thirdOperatorId&#34;: &#34;&#34;, &#34;thirdOrgId&#34;: &#34;&#34;}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Fri, 04 Jul 2025 07:08:52 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 72
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T293T17516129322881515
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;flowTemplateName&#39;: &#39;&#39;, &#39;docs&#39;: [{&#39;totalPage&#39;: 1, &#39;source&#39;: 0, &#39;fileId&#39;: &#39;ecaa9ad9490842aaacaae5b5e9f6a66e&#39;, &#39;fileName&#39;: &#39;模板.pdf&#39;, &#39;fileUrl&#39;: &#39;https://esignoss.esign.cn/1111563786/99ac6dc1-10a4-43f1-ac19-08aaac39aea7/%E6%A8%A1%E6%9D%BF.pdf?Expires=1751616532&amp;OSSAccessKeyId=LTAI5tHULnSmVesom4Uy5ENh&amp;Signature=p7IXhME78F02Tkif6szYyIVKelQ%3D&#39;}]}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>373</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>95.4</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>94.077</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_3">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">签署文件下载orgOid为空</td>
      <td style="text-align:center;width:6em;">80.34 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_3_1">log-1</a>
        <div id="popup_log_1_3_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_3_1">&times;</a>

            <div class="content">
              <h3>Name: 签署文件下载orgOid为空</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.31.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 3438757422
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Service-Group</strong>: DEFAULT
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 184
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;operatorOid&#34;: &#34;72526616536d40b9864c79cd1954dbd1&#34;, &#34;orgOid&#34;: &#34;421612cd9edc4f859ddc0808d5a7e321&#34;, &#34;taskId&#34;: &#34;3a805a461c3346a99de127a6cd395f24&#34;, &#34;thirdOperatorId&#34;: &#34;&#34;, &#34;thirdOrgId&#34;: &#34;&#34;}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Fri, 04 Jul 2025 07:08:52 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 59
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T293T17516129323841519
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;flowTemplateName&#39;: &#39;&#39;, &#39;docs&#39;: [{&#39;totalPage&#39;: 1, &#39;source&#39;: 0, &#39;fileId&#39;: &#39;ecaa9ad9490842aaacaae5b5e9f6a66e&#39;, &#39;fileName&#39;: &#39;模板.pdf&#39;, &#39;fileUrl&#39;: &#39;https://esignoss.esign.cn/1111563786/99ac6dc1-10a4-43f1-ac19-08aaac39aea7/%E6%A8%A1%E6%9D%BF.pdf?Expires=1751616532&amp;OSSAccessKeyId=LTAI5tHULnSmVesom4Uy5ENh&amp;Signature=p7IXhME78F02Tkif6szYyIVKelQ%3D&#39;}]}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>373</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>80.34</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>78.637</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_4">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">签署文件下载taskId为空</td>
      <td style="text-align:center;width:6em;">171.24 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_4_1">log-1</a>
        <div id="popup_log_1_4_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_4_1">&times;</a>

            <div class="content">
              <h3>Name: 签署文件下载taskId为空</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.31.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 3438757422
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Service-Group</strong>: DEFAULT
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 184
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;operatorOid&#34;: &#34;72526616536d40b9864c79cd1954dbd1&#34;, &#34;orgOid&#34;: &#34;421612cd9edc4f859ddc0808d5a7e321&#34;, &#34;taskId&#34;: &#34;3a805a461c3346a99de127a6cd395f24&#34;, &#34;thirdOperatorId&#34;: &#34;&#34;, &#34;thirdOrgId&#34;: &#34;&#34;}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Fri, 04 Jul 2025 07:08:52 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 143
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T293T17516129324711529
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;flowTemplateName&#39;: &#39;&#39;, &#39;docs&#39;: [{&#39;totalPage&#39;: 1, &#39;source&#39;: 0, &#39;fileId&#39;: &#39;ecaa9ad9490842aaacaae5b5e9f6a66e&#39;, &#39;fileName&#39;: &#39;模板.pdf&#39;, &#39;fileUrl&#39;: &#39;https://esignoss.esign.cn/1111563786/99ac6dc1-10a4-43f1-ac19-08aaac39aea7/%E6%A8%A1%E6%9D%BF.pdf?Expires=1751616532&amp;OSSAccessKeyId=LTAI5tHULnSmVesom4Uy5ENh&amp;Signature=p7IXhME78F02Tkif6szYyIVKelQ%3D&#39;}]}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>10000007</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>任务id不能为空</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>373</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>171.24</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>168.851</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_4">traceback</a>
          <div id="popup_attachment_1_4" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_4">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;/Library/Python/3.8/site-packages/httprunner/api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 10000007(int)	==&gt; fail
0(int) equals 10000007(int)

validate: content.message contains 任务id不能为空(str)	==&gt; fail
成功(str) contains 任务id不能为空(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;/Library/Python/3.8/site-packages/httprunner/api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 10000007(int)	==&gt; fail
0(int) equals 10000007(int)

validate: content.message contains 任务id不能为空(str)	==&gt; fail
成功(str) contains 任务id不能为空(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_5">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">签署文件下载thirdOperatorId为空</td>
      <td style="text-align:center;width:6em;">82.05 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_5_1">log-1</a>
        <div id="popup_log_1_5_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_5_1">&times;</a>

            <div class="content">
              <h3>Name: 签署文件下载thirdOperatorId为空</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.31.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 3438757422
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Service-Group</strong>: DEFAULT
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 184
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;operatorOid&#34;: &#34;72526616536d40b9864c79cd1954dbd1&#34;, &#34;orgOid&#34;: &#34;421612cd9edc4f859ddc0808d5a7e321&#34;, &#34;taskId&#34;: &#34;3a805a461c3346a99de127a6cd395f24&#34;, &#34;thirdOperatorId&#34;: &#34;&#34;, &#34;thirdOrgId&#34;: &#34;&#34;}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Fri, 04 Jul 2025 07:08:52 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 53
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T293T17516129327071535
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;flowTemplateName&#39;: &#39;&#39;, &#39;docs&#39;: [{&#39;totalPage&#39;: 1, &#39;source&#39;: 0, &#39;fileId&#39;: &#39;ecaa9ad9490842aaacaae5b5e9f6a66e&#39;, &#39;fileName&#39;: &#39;模板.pdf&#39;, &#39;fileUrl&#39;: &#39;https://esignoss.esign.cn/1111563786/99ac6dc1-10a4-43f1-ac19-08aaac39aea7/%E6%A8%A1%E6%9D%BF.pdf?Expires=1751616532&amp;OSSAccessKeyId=LTAI5tHULnSmVesom4Uy5ENh&amp;Signature=p7IXhME78F02Tkif6szYyIVKelQ%3D&#39;}]}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>373</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>82.05</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>80.764</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_6">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">签署文件下载thirdOrgId为空</td>
      <td style="text-align:center;width:6em;">67.77 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_6_1">log-1</a>
        <div id="popup_log_1_6_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_6_1">&times;</a>

            <div class="content">
              <h3>Name: 签署文件下载thirdOrgId为空</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.31.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 3438757422
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Service-Group</strong>: DEFAULT
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 184
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;operatorOid&#34;: &#34;72526616536d40b9864c79cd1954dbd1&#34;, &#34;orgOid&#34;: &#34;421612cd9edc4f859ddc0808d5a7e321&#34;, &#34;taskId&#34;: &#34;3a805a461c3346a99de127a6cd395f24&#34;, &#34;thirdOperatorId&#34;: &#34;&#34;, &#34;thirdOrgId&#34;: &#34;&#34;}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Fri, 04 Jul 2025 07:08:52 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 47
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T293T17516129327841537
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;flowTemplateName&#39;: &#39;&#39;, &#39;docs&#39;: [{&#39;totalPage&#39;: 1, &#39;source&#39;: 0, &#39;fileId&#39;: &#39;ecaa9ad9490842aaacaae5b5e9f6a66e&#39;, &#39;fileName&#39;: &#39;模板.pdf&#39;, &#39;fileUrl&#39;: &#39;https://esignoss.esign.cn/1111563786/99ac6dc1-10a4-43f1-ac19-08aaac39aea7/%E6%A8%A1%E6%9D%BF.pdf?Expires=1751616532&amp;OSSAccessKeyId=LTAI5tHULnSmVesom4Uy5ENh&amp;Signature=p7IXhME78F02Tkif6szYyIVKelQ%3D&#39;}]}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>373</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>67.77</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>66.493</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_7">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">使用taskId下载签署文件</td>
      <td style="text-align:center;width:6em;">73.86 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_7_1">log-1</a>
        <div id="popup_log_1_7_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_7_1">&times;</a>

            <div class="content">
              <h3>Name: 使用taskId下载签署文件</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.31.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 3438757422
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Service-Group</strong>: DEFAULT
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 184
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;operatorOid&#34;: &#34;72526616536d40b9864c79cd1954dbd1&#34;, &#34;orgOid&#34;: &#34;421612cd9edc4f859ddc0808d5a7e321&#34;, &#34;taskId&#34;: &#34;3a805a461c3346a99de127a6cd395f24&#34;, &#34;thirdOperatorId&#34;: &#34;&#34;, &#34;thirdOrgId&#34;: &#34;&#34;}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/mix/signflows/documents
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Fri, 04 Jul 2025 07:08:52 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 49
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T293T17516129328561539
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;flowTemplateName&#39;: &#39;&#39;, &#39;docs&#39;: [{&#39;totalPage&#39;: 1, &#39;source&#39;: 0, &#39;fileId&#39;: &#39;ecaa9ad9490842aaacaae5b5e9f6a66e&#39;, &#39;fileName&#39;: &#39;模板.pdf&#39;, &#39;fileUrl&#39;: &#39;https://esignoss.esign.cn/1111563786/99ac6dc1-10a4-43f1-ac19-08aaac39aea7/%E6%A8%A1%E6%9D%BF.pdf?Expires=1751616532&amp;OSSAccessKeyId=LTAI5tHULnSmVesom4Uy5ENh&amp;Signature=p7IXhME78F02Tkif6szYyIVKelQ%3D&#39;}]}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>373</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>73.86</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>72.56</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
  </table>
  
</body>