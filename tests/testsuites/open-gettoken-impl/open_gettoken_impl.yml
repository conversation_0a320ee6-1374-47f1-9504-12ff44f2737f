config:
    name: 需要登录获取token的接口汇总
    variables:
        Cookie: ${get_token()}

testcases:
#XFT平台相关接口

#    生成流程组信息:
#        testcase: testcases/XFT/Processgroup-related-interfaces/initProcessGroupUsingPOSTcase.yml
#
#    流程模版excel上传:
#        testcase: testcases/XFT/Processgroup-related-interfaces/importPreFillExcelUsingPOSTcase.yml


#ep平台相关接口

    获取渠道商商品展示页详情:
        testcase: testcases/ep/Get-the-order-related-interface/getChannelCommodityDetailUsingGETcase.yml

    获取渠道商商品展示页面地址:
        testcase: testcases/ep/Get-the-order-related-interface/getChannelCommodityUrlUsingGETcase.yml

    获取渠道商购买流程发起标识:
        testcase: testcases/ep/Get-the-order-related-interface/startChannelOrderUsingGETcase.yml

    试用商品下单:
        testcase: testcases/ep/Get-the-order-related-interface/trialOrderUsingPOSTcase.yml