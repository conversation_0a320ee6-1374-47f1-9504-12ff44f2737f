config:
    name: 不需要登录获取token接口汇总

testcases:
#XFT平台相关接口

    账户轩辕套餐余量:
        testcase: testcases/XFT/Account-related-interface/getAccountMarginUsingGETcase.yml

    场景信息:
        testcase: testcases/XFT/Account-related-interface/getMixSceneInfoUsingGETcase.yml

    查询电子签功能链接:
        testcase: testcases/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOSTcase.yml

    获取融合权限列表:
        testcase: testcases/XFT/Permission-related-list/listUsingGETcase.yml

    获取当前企业空间下模板列表:
        testcase: testcases/XFT/Processtemplate-related-interfaces/getSubjectFlowTemplatesUsingGETcase.yml

    批量撤回:
        testcase: testcases/XFT/Task-and-signingprocess/batchRevokeSignTaskUsingPOSTcase.yml

    批量催签:
        testcase: testcases/XFT/Task-and-signingprocess/batchRushSignTaskUsingPOSTcase.yml

    获取批量签署:
        testcase: testcases/XFT/Task-and-signingprocess/batchSignTaskUsingPOSTcase.yml

    签署文件下载:
        testcase: testcases/XFT/Task-and-signingprocess/getSignDocumentUsingPOSTcase.yml


#ep平台相关接口

    查询客户license:
        testcase: testcases/ep/Create-process-related-interfaces/licenseQuery_testcase.yml

    一步发起签署:
        testcase: testcases/ep/Create-process-related-interfaces/createFlowOneStepUsingPOSTcase.yml

    查询用户套餐余量:
        testcase: testcases/ep/User-plan-related-interface/ordersRemainingQuantity_testcase.yml

    获取《下单e签宝套餐》页面链接:
        testcase: testcases/ep/User-plan-related-interface/orgPlaceOrgUrl_testcase.yml