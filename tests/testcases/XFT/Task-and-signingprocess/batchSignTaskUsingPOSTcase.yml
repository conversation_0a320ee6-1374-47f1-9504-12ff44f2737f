- config:
    name: 获取批量签署

- test:
    name: 获取批量签署
    variables:
      processIds: ["245389c9c7ee4acfadf3c265d6a9ff5e"]
      redirectUrl: "https://www.esign.cn/"
    api: api/XFT/Task-and-signingprocess/batchSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取批量签署processIds为空
    variables:
      processIds: ['']
    api: api/XFT/Task-and-signingprocess/batchSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]
- test:
    name: 获取批量签署redirectUrl为空
    variables:
      redirectUrl: ''
    api: api/XFT/Task-and-signingprocess/batchSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]
