- config:
    name: 批量催签
- test:
    name: 批量催签operatorOid为空
    variables:
      operatorOid: ''
    api: api/XFT/Task-and-signingprocess/batchRushSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "参数错误: accountId不能为空"]

- test:
    name: 批量催签orgOid为空
    variables:
      orgOid: ''
    api: api/XFT/Task-and-signingprocess/batchRushSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "参数错误: subjectId不能为空"]

- test:
    name: 批量催签taskIds为空
    variables:
      taskIds: ['']
    api: api/XFT/Task-and-signingprocess/batchRushSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "合同流程不存在"]

- test:
    name: 批量催签thirdOperatorId为空
    variables:
      thirdOperatorId: ''
    api: api/XFT/Task-and-signingprocess/batchRushSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "没有可操作的合同流程"]

- test:
    name: 批量催签thirdOrgId为空
    variables:
      thirdOrgId: ''
    api: api/XFT/Task-and-signingprocess/batchRushSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "没有可操作的合同流程"]

