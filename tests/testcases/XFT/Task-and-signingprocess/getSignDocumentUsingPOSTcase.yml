- config:
    name: 签署文件下载
    variables:
      - operatorOid: "72526616536d40b9864c79cd1954dbd1"
      - orgOid: "421612cd9edc4f859ddc0808d5a7e321"
      - taskId: "3a805a461c3346a99de127a6cd395f24"
      - thirdOperatorId: ""
      - thirdOrgId: ""

- test:
    name: 签署文件下载
    variables:
     - json: {"operatorOid": "${operatorOid}", "orgOid": "${orgOid}", "taskId": "${taskId}", "thirdOperatorId": "", "thirdOrgId": ""}
#      operatorOid: "72526616536d40b9864c79cd1954dbd1"
#      orgOid: "421612cd9edc4f859ddc0808d5a7e321"
#      taskId: "3a805a461c3346a99de127a6cd395f24"
#      thirdOperatorId: ""
#      thirdOrgId: ""
    api: api/XFT/Task-and-signingprocess/getSignDocumentUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 签署文件下载operatorOid为空
    variables:
     - json: {"operatorOid": "", "orgOid": "${orgOid}", "taskId": "${taskId}", "thirdOperatorId": "", "thirdOrgId": ""}
    api: api/XFT/Task-and-signingprocess/getSignDocumentUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 签署文件下载orgOid为空
    variables:
      - json: {"operatorOid": "${operatorOid}", "orgOid": "", "taskId": "${taskId}", "thirdOperatorId": "", "thirdOrgId": ""}
    api: api/XFT/Task-and-signingprocess/getSignDocumentUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 签署文件下载taskId为空
    variables:
      - json: {"operatorOid": "${operatorOid}", "orgOid": "${orgOid}", "taskId": "", "thirdOperatorId": "", "thirdOrgId": ""}
    api: api/XFT/Task-and-signingprocess/getSignDocumentUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "任务id不能为空"]

- test:
    name: 签署文件下载thirdOrgId为空
    variables:
      - json: {"operatorOid": "${operatorOid}", "orgOid": "${orgOid}", "taskId": "${taskId}", "thirdOperatorId": "", "thirdOrgId": ""}
    api: api/XFT/Task-and-signingprocess/getSignDocumentUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]


- test:
    name: 使用taskId下载签署文件
    variables:
      - json: { "taskId": "e2bed6ce6ff34fbaade84900cf60ef14"}
    api: api/XFT/Task-and-signingprocess/getSignDocumentUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]