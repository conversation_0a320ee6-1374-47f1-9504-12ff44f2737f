- config:
    name: 批量撤回

- test:
    name: 批量撤回
    variables:
      operatorOid: "72526616536d40b9864c79cd1954dbd1"
      orgOid: "421612cd9edc4f859ddc0808d5a7e321"
      revokeReason: "测试"
      taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
      thirdOperatorId: ""
      thirdOrgId: ""
    api: api/XFT/Task-and-signingprocess/batchRevokeSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 批量撤回operatorOid为空
    variables:
      operatorOid: ''
    api: api/XFT/Task-and-signingprocess/batchRevokeSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "参数错误: accountId不能为空"]

- test:
    name: 批量撤回orgOid为空
    variables:
      orgOid: ''
    api: api/XFT/Task-and-signingprocess/batchRevokeSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "参数错误: subjectId不能为空"]

- test:
    name: 批量撤回revokeReason为空
    variables:
      revokeReason: ''
    api: api/XFT/Task-and-signingprocess/batchRevokeSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "撤回原因不能为空"]

- test:
    name: 批量撤回taskIds为空
    variables:
      taskIds: []
    api: api/XFT/Task-and-signingprocess/batchRevokeSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "参数错误: processIds不能为空"]

- test:
    name: 批量撤回thirdOperatorId为空
    variables:
      thirdOperatorId: ''
    api: api/XFT/Task-and-signingprocess/batchRevokeSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 批量撤回thirdOrgId为空
    variables:
      thirdOrgId: ''
    api: api/XFT/Task-and-signingprocess/batchRevokeSignTaskUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

