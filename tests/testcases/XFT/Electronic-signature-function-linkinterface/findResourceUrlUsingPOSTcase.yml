- config:
    name: 查询电子签功能链接

- test:
    name: 查询电子签功能链接-resourceCode为企业控制台
    variables:
      orgId: "40933ba2caae48f08fbc3eed19d578dd"
      psnId: "72526616536d40b9864c79cd1954dbd1"
      resourceCode: "ENTERPRISE_BASE"
      taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
    api: api/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 查询电子签功能链接-resourceCode为模版管理页
    variables:
      orgId: "40933ba2caae48f08fbc3eed19d578dd"
      psnId: "72526616536d40b9864c79cd1954dbd1"
      resourceCode: "TEMPLATE_MANAGE"
      taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
    api: api/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 查询电子签功能链接-resourceCode为直接填写页
    variables:
      orgId: "40933ba2caae48f08fbc3eed19d578dd"
      psnId: "72526616536d40b9864c79cd1954dbd1"
      resourceCode: "ENTER_FILL"
      taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
    api: api/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 查询电子签功能链接-resourceCode直接签署页
    variables:
      orgId: "40933ba2caae48f08fbc3eed19d578dd"
      psnId: "72526616536d40b9864c79cd1954dbd1"
      resourceCode: "ENTER_SIGN"
      taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
    api: api/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 查询电子签功能链接-resourceCode为发起签署页
    variables:
      orgId: "40933ba2caae48f08fbc3eed19d578dd"
      psnId: "72526616536d40b9864c79cd1954dbd1"
      resourceCode: "START_FLOW"
      taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
    api: api/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 查询电子签功能链接orgId为空
    variables:
      orgId: ""
      psnId: "72526616536d40b9864c79cd1954dbd1"
      resourceCode: "ENTERPRISE_BASE"
      taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
    api: api/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "企业账号oid不能为空"]
- test:
    name: 查询电子签功能链接psnId为空
    variables:
      orgId: "40933ba2caae48f08fbc3eed19d578dd"
      psnId: ""
      resourceCode: "ENTERPRISE_BASE"
      taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
    api: api/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "当前操作人账号psnId不能为空"]

- test:
    name: 查询电子签功能链接resourceCode为空
    variables:
      orgId: "40933ba2caae48f08fbc3eed19d578dd"
      psnId: "72526616536d40b9864c79cd1954dbd1"
      resourceCode: ""
      taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
    api: api/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "电子签功能页编码不能为空"]

- test:
    name: 查询电子签功能链接taskIds为空
    variables:
      orgId: "40933ba2caae48f08fbc3eed19d578dd"
      psnId: "72526616536d40b9864c79cd1954dbd1"
      resourceCode: "ENTERPRISE_BASE"
      taskIds:
    api: api/XFT/Electronic-signature-function-linkinterface/findResourceUrlUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "taskIds不能为空"]
