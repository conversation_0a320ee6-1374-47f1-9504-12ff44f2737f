- config:
    name: 流程模版excel上传


- test:
    name: 流程模版excel上传
    variables:
      fileKey: "$b162ffca-064e-431b-b697-39fdd6c1939e$2182231174"
      processGroupId: "PG-be13e2d086d243f49e11a44a7588f28a"
      flowTemplateId: ${ENV(flowTemplateId)}

    api: api/XFT/Processgroup-related-interfaces/importPreFillExcelUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 流程模版excel上传fileKey为空
    variables:
      fileKey: ''
      processGroupId: "PG-be13e2d086d243f49e11a44a7588f28a"
      flowTemplateId: ${ENV(flowTemplateId)}

    api: api/XFT/Processgroup-related-interfaces/importPreFillExcelUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 流程模版excel上传processGroupId为空
    variables:
      fileKey: ${ENV(fileKey)}
      processGroupId: ''
      flowTemplateId: ${ENV(flowTemplateId)}

    api: api/XFT/Processgroup-related-interfaces/importPreFillExcelUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 流程模版excel上传流程模板id为空
    variables:
      fileKey: ${ENV(fileKey)}
      processGroupId: "PG-be13e2d086d243f49e11a44a7588f28a"
      flowTemplateId: ''

    api: api/XFT/Processgroup-related-interfaces/importPreFillExcelUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

