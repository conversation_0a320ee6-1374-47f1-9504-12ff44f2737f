- config:
    name: 生成流程组信息
    base_url: ${ENV(footstone_mixscene_url)}
- test:
    name: 生成流程组信息
    variables:
      repeatValidate: true
      participantName: "王真真"
      participantMobile: "13213094836"
    api: api/XFT/Processgroup-related-interfaces/initProcessGroupUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 生成流程组信息participantMobile为空
    variables:
      repeatValidate: true
      participantName: "王真真"
      participantMobile: ""
    api: api/XFT/Processgroup-related-interfaces/initProcessGroupUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "参与人手机号不能为空"]


- test:
    name: 生成流程组信息participantName为空
    variables:
      repeatValidate: true
      participantName: ""
      participantMobile: "13213094836"
    api: api/XFT/Processgroup-related-interfaces/initProcessGroupUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "参与人姓名不能为空"]

- test:
    name: 生成流程组信息repeatValidate为空
    variables:
      repeatValidate: ''
      participantName: "王真真"
      participantMobile: "13213094836"
    api: api/XFT/Processgroup-related-interfaces/initProcessGroupUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]


