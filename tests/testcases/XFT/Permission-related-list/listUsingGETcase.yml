- config:
    name: 获取融合权限列表

- test:
    name: 获取融合权限列表
    variables:
      oid: "b1c4b1fb60bc4730a56c3a89fe33490a"
      orgOid: "d42d4d754cfa4af085d5737cf2e18d1c"
      platform: "薪福通"
    api: api/XFT/Permission-related-list/listUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取融合权限列表oid为空
    variables:
      oid: ''
    api: api/XFT/Permission-related-list/listUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取融合权限列表orgOid为空
    variables:
      orgOid: ''
    api: api/XFT/Permission-related-list/listUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取融合权限列表platform为空
    variables:
      platform: ''
    api: api/XFT/Permission-related-list/listUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

