- config:
    name: 获取当前企业空间下模板列表

- test:
    name: 获取当前企业空间下模板列表
    variables:
      flowTemplateName: "共用-测试模版1"
      pageNo: "1"
      pageSize: "10"
      status: ""
    api: api/XFT/Processtemplate-related-interfaces/getSubjectFlowTemplatesUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取当前企业空间下模板列表flowTemplateName为空
    variables:
      flowTemplateName: ''
    api: api/XFT/Processtemplate-related-interfaces/getSubjectFlowTemplatesUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取当前企业空间下模板列表pageNo为空
    variables:
      pageNo: ''
    api: api/XFT/Processtemplate-related-interfaces/getSubjectFlowTemplatesUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取当前企业空间下模板列表pageSize为空
    variables:
      pageSize: ''
    api: api/XFT/Processtemplate-related-interfaces/getSubjectFlowTemplatesUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取当前企业空间下模板列表status为空
    variables:
      status: ''
    api: api/XFT/Processtemplate-related-interfaces/getSubjectFlowTemplatesUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

