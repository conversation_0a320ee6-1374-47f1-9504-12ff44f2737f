
#发起时校验签署人二要素信息的合法性
- config:
    name: 发起时校验签署人二要素信息的合法性

- test:
    name: name为空
    api: api/SF/user/checkNameAndContacts_api.yml
    variables:
        - name: ""
        - contact : ""
    validate:
        - contains: ["content.message", "系统异常"]
        - eq: ["content.code", 10000001]

- test:
    name: contact为空
    api: api/SF/user/checkNameAndContacts_api.yml
    variables:
        - name: "雷后阳"
        - contact : ""
    validate:
        - contains: ["content.message", "系统异常"]
        - eq: ["content.code", 10000001]

- test:
    name: 正常场景
    api: api/SF/user/checkNameAndContacts_api.yml
    variables:
        - name: "雷后阳"
        - contact : "15988861343"
    validate:
        - eq: ["content.message", "成功"]
        - eq: ["content.code", 0]