#查询员工列表
- config:
    name: 查询员工列表

- test:
    name: page为空
    api: api/SF/user/userSearch_api.yml
    variables:
        - page: ""
        - pageSize : 15
        - name: ""
        - mobile : ""
    validate:
        - eq: ["content.message", "成功"]
        - len_eq: ["content.data.items", 15]
        - greater_than: ["content.data.total", 0]

- test:
    name: pageSize为空
    api: api/SF/user/userSearch_api.yml
    variables:
        - page: 1
        - pageSize : ""
        - name: ""
        - mobile : ""
    validate:
        - eq: ["content.message", "成功"]
        - len_eq: ["content.data.items", 15]
        - greater_than: ["content.data.total", 0]


- test:
    name: 根据姓名查询
    api: api/SF/user/userSearch_api.yml
    variables:
        - name: "王慧"
        - mobile : ""
        - page: 1
        - pageSize : 10
    validate:
        - eq: ["content.message", "成功"]
        - len_ge: ["content.data.items", 1]
        - greater_than: ["content.data.total", 0]
- test:
    name: 根据手机号码查询
    api: api/SF/user/userSearch_api.yml
    variables:
        - name: ""
        - mobile : "13336076547"
        - page: 1
        - pageSize : 10
    validate:
        - eq: ["content.message", "成功"]
        - len_ge: ["content.data.items", 1]
        - greater_than: ["content.data.total", 0]

- test:
    name: 根据姓名+手机号码查询
    api: api/SF/user/userSearch_api.yml
    variables:
        - name: "王慧"
        - mobile : "13336076547"
        - page: 1
        - pageSize : 10
    validate:
        - eq: ["content.message", "成功"]
        - len_ge: ["content.data.items", 1]
        - greater_than: ["content.data.total", 0]