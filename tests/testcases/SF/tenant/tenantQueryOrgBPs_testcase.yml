
#查询企业通讯录列表
- config:
    name: 查询企业通讯录列表
- test:
    name: 无条件查询
    api: api/SF/tenant/tenantQueryOrgBPs_api.yml
    variables:
        - companyName: ""
        - adminName : ""
        - adminMobile : ""
        - adminEmail : ""
        - page : "1"
        - pageSize : "10"
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]

- test:
    name: 根据companyName查询
    api: api/SF/tenant/tenantQueryOrgBPs_api.yml
    variables:
        - companyName: "上海灵契软件中心"
        - adminName : ""
        - adminMobile : ""
        - adminEmail : ""
        - page : ""
        - pageSize : ""
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]

- test:
    name: 根据adminName查询
    api: api/SF/tenant/tenantQueryOrgBPs_api.yml
    variables:
        - companyName: ""
        - adminName : "杨阔"
        - adminMobile : ""
        - adminEmail : ""
        - page : ""
        - pageSize : ""
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]

- test:
    name: 根据adminMobile查询
    api: api/SF/tenant/tenantQueryOrgBPs_api.yml
    variables:
        - companyName: ""
        - adminName : ""
        - adminMobile : "13636630275"
        - adminEmail : ""
        - page : ""
        - pageSize : ""
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]

- test:
    name: 根据adminEmail查询
    api: api/SF/tenant/tenantQueryOrgBPs_api.yml
    variables:
        - companyName: ""
        - adminName : ""
        - adminMobile : ""
        - adminEmail : "<EMAIL>"
        - page : ""
        - pageSize : ""
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]