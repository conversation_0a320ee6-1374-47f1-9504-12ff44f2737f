#使用拟定流程批量进行发起签署
- config:
    name: 使用拟定流程批量进行发起签署
- test:
    name: step1:拟定一个合同
    api: api/SF/drawUpFlow/drawUpFlowCreate_api.yml
    variables :
        templateFlowId : "2496775860217449777"
    validate:
        - eq: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - ne: ["content.data.drawUpFlowId", null]
    extract:
        - drawUpFlowId1: content.data.drawUpFlowId

- test:
    name: step2:拟定第二个合同
    api: api/SF/drawUpFlow/drawUpFlowCreate_api.yml
    variables:
        templateFlowId: "2496775860217449777"
    validate:
        - eq: [ "content.message", "成功" ]
        - eq: [ "content.code", 0 ]
        - ne: [ "content.data.drawUpFlowId", null ]
    extract:
        - drawUpFlowId2: content.data.drawUpFlowId

- test:
      name: step3:拟定第三个合同
      api: api/SF/drawUpFlow/drawUpFlowCreate_api.yml
      variables:
          templateFlowId: "2496775860217449777"
      validate:
          - eq: [ "content.message", "成功" ]
          - eq: [ "content.code", 0 ]
          - ne: [ "content.data.drawUpFlowId", null ]
      extract:
          - drawUpFlowId3: content.data.drawUpFlowId

- test:
    name: 正常场景-使用拟定流程进行发起签署(一个流程)
    api: api/SF/drawUpFlow/drawUpFlowStartSign_api.yml
    variables :
        - json : {"drawUpFlowIds": [ "$drawUpFlowId1" ]}
    validate:
        - eq: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - eq: ["content.data", true]

- test:
      name: 正常场景-使用拟定流程进行发起签署(两个流程)
      api: api/SF/drawUpFlow/drawUpFlowStartSign_api.yml
      variables:
          - drawUpFlowIds: "2634788863583198156"
          - json: { "drawUpFlowIds": [ "$drawUpFlowId2","$drawUpFlowId3" ] }
      validate:
          - eq: [ "content.message", "成功" ]
          - eq: [ "content.code", 0 ]
          - eq: [ "content.data", true ]
- test:
      name: 异常场景- drawUpFlowIds非法
      api: api/SF/drawUpFlow/drawUpFlowStartSign_api.yml
      variables:
          - json: { "drawUpFlowIds": [ "1258000" ] }
      validate:
          - eq: [ "content.message", "成功" ]
          - eq: [ "content.code", 0 ]
          - eq: [ "content.data", false ]


