#撤销拟定合同流程
- config:
    name: 撤销拟定合同流程
- test:
    name: 异常场景-撤销拟定合同流程-流程不存在
    api: api/SF/drawUpFlow/drawUpFlowBatchRevoke_api.yml
    variables :
        json :
          {
              "drawUpFlowIds": [
                  "1258000"
              ],
              "revokeReason": "集成测试-撤销合同",
              "operatorUserId": "tsign1",
          }
    validate:
        - eq: ["content.message", "合同拟定流程不存在"]
        - ne: ["content.code", 0]


- test:
    name: step1:拟定一个合同
    api: api/SF/drawUpFlow/drawUpFlowCreate_api.yml
    variables :
        templateFlowId : "2496775860217449777"
    validate:
        - eq: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - ne: ["content.data.drawUpFlowId", null]
    extract:
        - drawUpFlowId1: content.data.drawUpFlowId

- test:
    name: step2:拟定第二个合同
    api: api/SF/drawUpFlow/drawUpFlowCreate_api.yml
    variables:
        templateFlowId: "2496775860217449777"
    validate:
        - eq: [ "content.message", "成功" ]
        - eq: [ "content.code", 0 ]
        - ne: [ "content.data.drawUpFlowId", null ]
    extract:
        - drawUpFlowId2: content.data.drawUpFlowId

- test:
      name: step3:拟定第三个合同
      api: api/SF/drawUpFlow/drawUpFlowCreate_api.yml
      variables:
          templateFlowId: "2496775860217449777"
      validate:
          - eq: [ "content.message", "成功" ]
          - eq: [ "content.code", 0 ]
          - ne: [ "content.data.drawUpFlowId", null ]
      extract:
          - drawUpFlowId3: content.data.drawUpFlowId

- test:
    name: 正常场景-撤销拟定合同流程（一个合同）
    api: api/SF/drawUpFlow/drawUpFlowBatchRevoke_api.yml
    variables :
        json :
          {
              "drawUpFlowIds": [
                  "$drawUpFlowId1"
              ],
              "revokeReason": "集成测试-撤销合同",
              "operatorUserId": "tsign1",
          }
    validate:
        - eq: ["content.message", "成功"]
        - eq: ["content.code", 0]

- test:
    name: 正常场景-撤销拟定合同流程（两个合同）
    api: api/SF/drawUpFlow/drawUpFlowBatchRevoke_api.yml
    variables :
        json :
          {
              "drawUpFlowIds": [
                  "$drawUpFlowId2",
                  "$drawUpFlowId3"
              ],
              "revokeReason": "集成测试-撤销合同",
              "operatorUserId": "tsign1",
          }
    validate:
        - eq: ["content.message", "成功"]
        - eq: ["content.code", 0]

