#查询模板详情

- config:
    name: 查询模板详情
- test:
    name: 正常场景-查询模板详情
    api: api/SF/templateFlow/templateFlowId_api.yml
    variables:
        templateFlowId: "2471267212900959410"
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - eq: ["content.data.templateFlowId", "2401504664920529238"]

- test:
    name: 异常场景-查询模板详情-模板不存在
    api: api/SF/templateFlow/templateFlowId_api.yml
    variables:
        templateFlowId: "125800000"
    validate:
        - contains: ["content.message", "没有找到流程模板"]
        - eq: ["content.code", 90004008]
