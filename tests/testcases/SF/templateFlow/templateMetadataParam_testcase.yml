#查询已发布模板列表
- config:
    name: 查询模板数据实体
- test:
    name: 正常场景-查询模板数据实体
    api: api/SF/templateFlow/templateMetadataParam_api.yml
    variables:
        templateFlowId: "2496775860217449777"
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - eq: ["content.data.0.templateFlowId", "2496775860217449777"]

- test:
    name: 异常场景-查询模板数据实体-模板不存在
    api: api/SF/templateFlow/templateMetadataParam_api.yml
    variables:
        templateFlowId: "125800000"
    validate:
        - eq: ["content.message", "没有找到流程模板"]
        - eq: ["content.code", 90004008]
        - eq: ["content.data", null]
          
