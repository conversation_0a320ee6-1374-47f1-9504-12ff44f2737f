- config:
    name: 获取渠道商购买流程发起标识

- test:
    name: 获取渠道商购买流程发起标识
    variables:
      buyerOid: '42d6869f2e5542309e01d8ea67988692'
      linkId: 'LINK-65c7c01677af4239bef044674c460c18'
      thirdAppId: '7876647500'
    api: api/ep/Get-the-order-related-interface/startChannelOrderUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取渠道商购买流程发起标识buyerOid为空
    variables:
      buyerOid: ''
      linkId: 'LINK-65c7c01677af4239bef044674c460c18'
      thirdAppId: '7876647500'
    api: api/ep/Get-the-order-related-interface/startChannelOrderUsingGET.yml
    validate:
      - eq: ["content.code", 10000001]
      - contains: ["content.message", "缺少参数: ouid"]
    teardown_hooks:
      - ${sleep($response,2)}

- test:
    name: 获取渠道商购买流程发起标识thirdAppId为空
    variables:
      buyerOid: '42d6869f2e5542309e01d8ea67988692'
      linkId: 'LINK-65c7c01677af4239bef044674c460c18'
      thirdAppId: ''
    api: api/ep/Get-the-order-related-interface/startChannelOrderUsingGET.yml
    validate:
      - eq: ["content.code", 14440106]
      - contains: ["content.message", "获取appId详情失败"]
