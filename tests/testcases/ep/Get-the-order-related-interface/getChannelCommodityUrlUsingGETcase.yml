- config:
    name: 获取渠道商商品展示页面地址
- test:
    name: 获取渠道商商品展示页面地址
    variables:
      authKey: '29f563c1-839d-4537-b6cc-fd26905a7d98'
      buyerOid: 'e23768d069c34d18a2e241efb291f3e9'
      commodityIds: ''
    api: api/ep/Get-the-order-related-interface/getChannelCommodityUrlUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取渠道商商品展示页面地址authKey为空
    variables:
      authKey: ''
      buyerOid: 'e23768d069c34d18a2e241efb291f3e9'
      commodityIds: ''
    api: api/ep/Get-the-order-related-interface/getChannelCommodityUrlUsingGET.yml
    validate:
      - eq: ["content.code", 14440002]
      - contains: ["content.message", "参数为空"]

- test:
    name: 获取渠道商商品展示页面地址buyerOid为空
    variables:
      authKey: '29f563c1-839d-4537-b6cc-fd26905a7d98'
      buyerOid: ''
      commodityIds: ''
    api: api/ep/Get-the-order-related-interface/getChannelCommodityUrlUsingGET.yml
    validate:
      - eq: ["content.code", 14440002]
      - contains: ["content.message", "参数为空"]

- test:
    name: 获取渠道商商品展示页面地址commodityIds为空
    variables:
      authKey: '29f563c1-839d-4537-b6cc-fd26905a7d98'
      buyerOid: 'e23768d069c34d18a2e241efb291f3e9'
      commodityIds: ''
    api: api/ep/Get-the-order-related-interface/getChannelCommodityUrlUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

