- config:
    name: 获取渠道商商品展示页详情

- test:
    name: 获取渠道商商品展示页详情
    variables:
      buyerOid: '41ec32df381f4316888205758127f27f'
      commodityIds: ''
      specificationIds: ''
      thirdAppId: "7876646593"

    api: api/ep/Get-the-order-related-interface/getChannelCommodityDetailUsingGET.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 获取渠道商商品展示页详情buyerOid不存在
    variables:
      buyerOid: '41ec32df381f4316888205758127f27'
      commodityIds: ''
      specificationIds: ''
      thirdAppId: '7876646593'

    api: api/ep/Get-the-order-related-interface/getChannelCommodityDetailUsingGET.yml
    validate:
      - eq: ["content.code", 14440105]
      - contains: ["content.message", "获取用户信息失败,ouid=41ec32df381f4316888205758127f27，open user不存在. ouid:41ec32df381f4316888205758127f27"]

- test:
    name: 获取渠道商商品展示页详情buyerOid为空
    variables:
      buyerOid: ''
      commodityIds: ''
      specificationIds: ''
      thirdAppId: '7876646593'

    api: api/ep/Get-the-order-related-interface/getChannelCommodityDetailUsingGET.yml
    validate:
      - eq: ["content.code", 10000001]
      - contains: ["content.message", "缺少参数: ouid"]

- test:
    name: 获取渠道商商品展示页详情thirdAppId不存在
    variables:
      buyerOid: '41ec32df381f4316888205758127f27f'
      commodityIds: ''
      specificationIds: ''
      thirdAppId: '6876646593'
    api: api/ep/Get-the-order-related-interface/getChannelCommodityDetailUsingGET.yml
    validate:
      - eq: ["content.code", 10000001]
      - contains: ["content.message", "系统异常"]

- test:
    name: 获取渠道商商品展示页详情thirdAppId为空
    variables:
      buyerOid: '41ec32df381f4316888205758127f27f'
      commodityIds: ''
      specificationIds: ''
      thirdAppId: ''
    api: api/ep/Get-the-order-related-interface/getChannelCommodityDetailUsingGET.yml
    validate:
      - eq: ["content.code", 14440106]
      - contains: ["content.message", "获取appId详情失败"]
