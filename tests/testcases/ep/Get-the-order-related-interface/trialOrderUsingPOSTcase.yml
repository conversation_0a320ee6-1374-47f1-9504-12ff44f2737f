- config:
    name: 试用商品下单

- test:
    name: 试用商品下单
    variables:
      activeNo: "Esign_partner_give_plan_5"
      appId: "7876647500"
      buyerOid: "2f2ebfdcf08049008be90bba8fb3ec7c"
    api: api/ep/Get-the-order-related-interface/trialOrderUsingPOST.yml
    validate:
      - eq: ["content.code", 14440810]
      - contains: ["content.message", "非新用户，无法购买试用商品"]

- test:
    name: 试用商品下单activeNo为空
    variables:
      activeNo: ""
      appId: "7876647500"
      buyerOid: "2f2ebfdcf08049008be90bba8fb3ec7c"
    api: api/ep/Get-the-order-related-interface/trialOrderUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "优惠活动编号id不能为空"]

- test:
    name: 试用商品下单appId为空
    variables:
      activeNo: "Esign_partner_give_plan_5"
      appId: ""
      buyerOid: "2f2ebfdcf08049008be90bba8fb3ec7c"
    api: api/ep/Get-the-order-related-interface/trialOrderUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "appid不能为空"]

- test:
    name: 试用商品下单buyerOid为空
    variables:
      activeNo: "Esign_partner_give_plan_5"
      appId: "7876647500"
      buyerOid: ""
    api: api/ep/Get-the-order-related-interface/trialOrderUsingPOST.yml
    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", "购买方oid不能为空"]
