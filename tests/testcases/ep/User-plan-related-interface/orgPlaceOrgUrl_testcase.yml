#获取《下单e签宝套餐》页面链接
- config:
    name: 获取《下单e签宝套餐》页面链接

- test:
    name: orgId为空
    api: api/ep/User-plan-related-interface/orgPlaceOrderUrl_api.yml
    variables:
        - json:
              {
                "commodityIds": "",
                "customBizNum": "",
                "notifyUrl": "",
                "orgId": "",
                "redirectUrl": "",
                "specificationIds": "",
                "transactorPsnId": "${ENV(ep_org_operator_psnId)}"
             }
    validate:
        - eq: ["content.message", "机构账号id不能为空"]
        - eq: ["content.code", 10000007]
        - eq: ["content.data", null]

- test:
    name: transactorPsnId为空
    api: api/ep/User-plan-related-interface/orgPlaceOrderUrl_api.yml
    variables:
        - json:
              {
                "commodityIds": "",
                "customBizNum": "",
                "notifyUrl": "",
                "orgId": "${ENV(ep_orgId)}",
                "redirectUrl": "",
                "specificationIds": "",
                "transactorPsnId": ""
             }
    validate:
        - eq: ["content.message", "经办人个人账号id不能为空"]
        - eq: ["content.code", 10000007]
        - eq: ["content.data", null]

- test:
    name: 经办人非企业成员
    api: api/ep/User-plan-related-interface/orgPlaceOrderUrl_api.yml
    variables:
        - json:
              {
                "commodityIds": "",
                "customBizNum": "",
                "notifyUrl": "",
                "orgId": "${ENV(unAuth_orgId)}",
                "redirectUrl": "",
                "specificationIds": "",
                "transactorPsnId": "${ENV(ep_org_operator_psnId)}"
             }
    validate:
        - contains: ["content.message", "get_place_order_url"]
        - ne: ["content.code", 0]
        - eq: ["content.data", null]

- test:
    name: 企业未授权
    api: api/ep/User-plan-related-interface/orgPlaceOrderUrl_api.yml
    variables:
        - json:
              {
                "commodityIds": "",
                "customBizNum": "",
                "notifyUrl": "",
                "orgId": "${ENV(unAuth_orgId)}",
                "redirectUrl": "",
                "specificationIds": "",
                "transactorPsnId": "${ENV(unAuth_org_operator_psnId)}"
             }
    validate:
        - contains: ["content.message", "get_place_order_url"]
        - contains: ["content.message", "${ENV(unAuth_orgId)}"]
        - ne: ["content.code", 0]
        - eq: ["content.data", null]

- test:
    name: commodityIds非法，数据不存在
    api: api/ep/User-plan-related-interface/orgPlaceOrderUrl_api.yml
    variables:
        - json:
              {
                "commodityIds": "1258000",
                "customBizNum": "",
                "notifyUrl": "",
                "orgId": "${ENV(ep_orgId)}",
                "redirectUrl": "",
                "specificationIds": "",
                "transactorPsnId": "${ENV(ep_org_operator_psnId)}"
             }
    validate:
        - eq: ["content.message", "商品无效，请确认商品是否存在且配置了展示权限，商品ids: 1258000"]
        - eq: ["content.code", 14440808]
        - eq: ["content.data", null]

- test:
    name: specificationIds非法，数据不存在
    api: api/ep/User-plan-related-interface/orgPlaceOrderUrl_api.yml
    variables:
        - json:
              {
                "commodityIds": "",
                "customBizNum": "",
                "notifyUrl": "",
                "orgId": "${ENV(ep_orgId)}",
                "redirectUrl": "",
                "specificationIds": "1258000",
                "transactorPsnId": "${ENV(ep_org_operator_psnId)}"
             }
    validate:
        - eq: ["content.message", "商品规格无效，请确认商品规格是否存在且配置了展示权限，规格ids: 1258000"]
        - eq: ["content.code", 14440811]
        - eq: ["content.data", null]


- test:
    name: 正常场景-返回所有商品链接-指定重定向地址-指定回调地址
    api: api/ep/User-plan-related-interface/orgPlaceOrderUrl_api.yml
    variables:
        - json:
              {
                "commodityIds": "",
                "customBizNum": "",
                 "redirectUrl": "https://bilibili.com",
                "orgId": "${ENV(ep_orgId)}",
                "notifyUrl": "http://172.24.7.81:8080/testnotify/msgRecive",
                "specificationIds": "",
                "transactorPsnId": "${ENV(ep_org_operator_psnId)}"
             }
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - ne: ["content.data.orgPlaceOrderUrl", null]

- test:
    name: 正常场景-指定返回1345（融合专用版）商品
    api: api/ep/User-plan-related-interface/orgPlaceOrderUrl_api.yml
    variables:
        - json:
              {
                "commodityIds": "1345",
                "customBizNum": "",
                "notifyUrl": "",
                "orgId": "${ENV(ep_orgId)}",
                "redirectUrl": "",
                "specificationIds": "",
                "transactorPsnId": "${ENV(ep_org_operator_psnId)}"
             }
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - ne: ["content.data.orgPlaceOrderUrl", null]

- test:
    name: 正常场景-指定返回1440规格
    api: api/ep/User-plan-related-interface/orgPlaceOrderUrl_api.yml
    variables:
        - json:
              {
                "commodityIds": "",
                "customBizNum": "",
                "notifyUrl": "",
                "orgId": "${ENV(ep_orgId)}",
                "redirectUrl": "",
                "specificationIds": "1440",
                "transactorPsnId": "${ENV(ep_org_operator_psnId)}"
             }
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - ne: ["content.data.orgPlaceOrderUrl", null]