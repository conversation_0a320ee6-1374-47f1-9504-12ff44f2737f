#查询用户套餐余量
- config:
    name: 查询用户套餐余量

- test:
    name: orgOid为空
    api: api/ep/User-plan-related-interface/ordersRemainingQuantity_api.yml
    variables:
        - orgId: ""
        - distributor: false
    validate:
        - eq: ["content.message", "机构账号id不能为空"]
        - ne: ["content.code", 0]

- test:
    name: 企业未授权
    api: api/ep/User-plan-related-interface/ordersRemainingQuantity_api.yml
    variables:
        - orgId: ${ENV(unAuth_orgId)}
        - distributor: false
    validate:
        - contains: ["content.message","$orgId"]
        - contains: ["content.message","未授权"]
        - contains: ["content.message","get_place_order_url"]
        - eq: [ "content.code", 14440806 ]

- test:
    name: 正常场景-返回企业套餐余量-distributor:false
    api: api/ep/User-plan-related-interface/ordersRemainingQuantity_api.yml
    variables:
        - orgId: ${ENV(ep_orgId)}
        - distributor: false
    validate:
        - eq: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - ne: ["content.data", null]
        - eq: ["content.data.containUnlimitedOrder", False]
        - contains: ["content.data", "totalRemainingQuantity"]

- test:
    name: 正常场景-返回企业套餐余量-distributor:true
    api: api/ep/User-plan-related-interface/ordersRemainingQuantity_api.yml
    variables:
        - orgId: ${ENV(ep_orgId)}
        - distributor: true
    validate:
        - eq: ["content.message", "成功"]
        - eq: ["content.code", 0]
        - eq: ["content.data.containUnlimitedOrder", False]
        - contains: ["content.data", "totalRemainingQuantity"]