- config:
    name: 一步发起签署
- test:
    name: 一步发起签署
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"

    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 一步发起签署attachmentName为空
    variables:
      attachmentName: ""
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"

    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "附件名称不能为空"]
- test:
    name: 一步发起签署fileId为空
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: ""
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "不能为空"]

- test:
    name: 一步发起签署fileName为空
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: ""
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 一步发起签署license为空
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: ""
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "license不能为空"]

- test:
    name: 一步发起签署noticeDeveloperUrl为空
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: ""
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 一步发起签署orgOid为空
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: ""
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "签署主体（企业oid）不能为空"]


- test:
    name: 一步发起签署signTheme为空
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: ""
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "签署主题不能为空"]


- test:
    name: 一步发起签署recipientAccount为空
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 一步发起签署recipientAccountId为空
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: ""
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "抄送人recipientAccountId和recipientAccount不可同时为空"]

- test:
    name: 一步发起签署recipientIdentityAccountId为空
    variables:
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      attachmentName: "劳动合同.pdf"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: ""
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "参与主体企业信息不能为空"]

- test:
    name: 一步发起签署recipientIdentityAccountType为空
    variables:
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      attachmentName: "劳动合同.pdf"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: ""
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "抄送人主体类型不能为空"]



- test:
    name: 一步发起签署posPage为空
    variables:
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      attachmentName: "劳动合同.pdf"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: ""
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "签署区位置页码信息不能为空"]

- test:
    name: 一步发起签署posX为空
    variables:
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      attachmentName: "劳动合同.pdf"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: ""
      posY: "100"
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "签署区位置X坐标不能为空"]


- test:
    name: 一步发起签署posY为空
    variables:
      attachmentName: "劳动合同.pdf"
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: ""
      sealId: ""
      thirdOrderNo: "2e32323425434546567678"
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "签署区位置Y坐标不能为空"]

- test:
    name: 一步发起签署thirdOrderNo为空
    variables:
      attachment_fileId: "389036cf40d14733a38b0fefc3e14f1a"
      attachmentName: "劳动合同.pdf"
      fileId: "389036cf40d14733a38b0fefc3e14f1a"
      fileName: "劳动合同.pdf"
      license: "2HRF4V3QGG7DG"
      noticeDeveloperUrl: "www.baidu.com"
      orgOid: "41ec32df381f4316888205758127f27f"
      signTheme: "一步发起签署-签署主题"
      addSignTime: false
      recipientAccount: ""
      recipientAccountId: "e491b0b9c75540e087cee2c635c7e611"
      recipientIdentityAccountId: "41ec32df381f4316888205758127f27f"
      recipientIdentityAccountType: "1"
      posPage: "1"
      posX: "500"
      posY: "100"
      sealId: ""
      thirdOrderNo: ""
    api: api/ep/Create-process-related-interfaces/createFlowOneStepUsingPOST.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]
