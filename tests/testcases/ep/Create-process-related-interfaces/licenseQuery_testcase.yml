#查询License列表
- config:
    name: 查询企业License列表

- test:
    name: orgOid为空
    api: api/ep/Create-process-related-interfaces/licenseQuery_api.yml
    variables:
        - orgOid: " "
        - page: 1
        - pageSize : 10
    validate:
        - eq: ["content.message", "企业orgOid不能为空"]
        - eq: ["content.code", 10000007]

- test:
    name: page为空
    api: api/ep/Create-process-related-interfaces/licenseQuery_api.yml
    variables:
        - orgOid: ${ENV(ep_orgId)}
        - page: ""
        - pageSize : 10
    validate:
        - eq: ["content.message", "系统异常"]
        - ne: ["content.code", 0]

- test:
    name: pageSize为空
    api: api/ep/Create-process-related-interfaces/licenseQuery_api.yml
    variables:
        - orgOid: ${ENV(ep_orgId)}
        - page: 1
        - pageSize : ""
    validate:
        - eq: ["content.message", "系统异常"]
        - ne: ["content.code", 0]
- test:
    name: orgOid在系统中不存在
    api: api/ep/Create-process-related-interfaces/licenseQuery_api.yml
    variables:
        - orgOid: "aaaaa10086"
        - page: 1
        - pageSize : 10
    validate:
        - eq: ["content.message", "用户未授权 :aaaaa10086, get_place_order_url"]
        - eq: ["content.code", 14440806]

- test:
    name: 企业未授权
    api: api/ep/Create-process-related-interfaces/licenseQuery_api.yml
    variables:
        - orgOid: ${ENV(unAuth_orgId)}
        - page: 1
        - pageSize : 10
    validate:
        - eq: ["content.message", "用户未授权 :${ENV(unAuth_orgId)}, get_place_order_url"]
        - eq: ["content.code", 14440806]

- test:
    name: 正常场景-返回企业license 列表
    api: api/ep/Create-process-related-interfaces/licenseQuery_api.yml
    variables:
        - orgOid: ${ENV(ep_orgId)}
        - page: 1
        - pageSize : 10
    validate:
        - contains: ["content.message", "成功"]
        - eq: ["content.code", 0]