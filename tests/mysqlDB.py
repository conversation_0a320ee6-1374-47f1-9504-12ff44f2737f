
import pymysql
from tools.readconfig import ReadConfig
import threading


mutex = threading.Lock()

mysql = ReadConfig()

class MysqlDb:
    def __init__(self):
        self.host = mysql.Database("host")
        self.user = mysql.Database("user")
        self.password = mysql.Database("password")
        self.port = mysql.Database("port")
        self.database = mysql.Database("database")
        self.db = pymysql.connect(host=self.host, user=self.user, password=self.password, database=self.database, charset="utf8")

    def execute(self,sql,data):
        connect = self.db
        try:
            connect.ping(reconnect=True)
            cursor = connect.cursor()
            mutex.acquire()
            execute_count = cursor.execute(data)
            mutex.release()
            connect.commit()
            connect.cursor()
            connect.close()
            print('执行语句:'+data +"，影响行：" + str(execute_count))
            return execute_count

        except Exception as e:
            print(e)
            connect.rollback()

    def execute_read(self,sql,data):
        connect = self.db
        cursor = connect.cursor()
        mutex.acquire()
        cursor.execute(sql)
        for row in cursor.fetchall():
            return row
        mutex.release()
        connect.commit()
        connect.cursor()
        connect.close()

    def delete_test_scope(self):
        sql = mysql.Mysql("del_scope")
        data = self.execute(self, sql)
        return data





