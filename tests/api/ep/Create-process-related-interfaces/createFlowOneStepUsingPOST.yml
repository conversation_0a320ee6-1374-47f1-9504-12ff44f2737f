name: 一步发起签署

request:
  url: ${ENV(mix_open_url)}/v1/mix/signflows/createFlowOneStep
  headers:
    Content-Type: application/json
    X-Tsign-Open-Auth-Mode: simple
    X-Tsign-Open-App-Id: ${ENV(appid_signflow)}
    X-Tsign-Service-Group: ${ENV(group)}

  method: post
  json:
    {
      "attachments": [
      {
        "attachmentName": $attachmentName,
        "fileId": $attachment_fileId
      }
      ],
      "docs": [
      {
        "fileId": $fileId,
        "fileName": $fileName
      }
      ],
      "license": $license,
      "noticeDeveloperUrl": $noticeDeveloperUrl,
      "orgOid": $orgOid,
      "signTheme": $signTheme,
      "signer": {
        "signFields": [
        {
          "addSignTime": false,
          "fileId": $fileId,
          "posBean": {
            "posPage": $posPage,
            "posX": $posX,
            "posY": $posY
          },
          "sealId": $sealId
        }
        ],
        "thirdOrderNo": $thirdOrderNo
      },
      "recipients": [
      {
        "recipientIdentityAccountType": $recipientIdentityAccountType,
        "recipientAccountId": $recipientAccountId,
        "recipientAccount": $recipientAccount,
        "recipientIdentityAccountId": $recipientIdentityAccountId

      }
      ],
      "thirdOrderNo": $thirdOrderNo
    }
