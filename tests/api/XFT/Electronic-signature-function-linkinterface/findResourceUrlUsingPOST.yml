name: 查询电子签功能链接
base_url: ${ENV(footstone_mixscene_url)}

variables:
  appId: ${ENV(appid1)}
  "orgId": "40933ba2caae48f08fbc3eed19d578dd"
  "psnId": "72526616536d40b9864c79cd1954dbd1"
  "resourceCode": "ENTERPRISE_BASE"
  "resourceData":
    "taskIds": ["668fa63fb07e4013b2d1291a88f5d80b"]
request:
  headers:
    Content-Type: application/json
    X-Tsign-Open-App-Id: $appId
    X-Tsign-Service-Group: ${ENV(group)}
  url: /v3/mix/scene/esign-resource-url
  method: post
  json:
    {
        "orgId": $orgId,
        "psnId": $psnId,
        "resourceCode": $resourceCode,
        "resourceData":{
          "taskIds": $taskIds
        }
    }

