name: 获取当前企业空间下模板列表
base_url: ${ENV(mix_open_url)}
variables:
  appId: ${ENV(appid)}
  flowTemplateName: "共用-测试模版1"
  pageNo: "1"
  pageSize: "10"
  status: ""
request:
  headers:
    Content-Type: application/json
    X-Tsign-Open-App-Id: $appId
    X-Tsign-Service-Group: ${ENV(group)}
    X-Tsign-Open-Auth-Mode: simple
    X-Tsign-Open-Tenant-Id: ${ENV(TenantId1)}
    X-Tsign-Open-Operator-Id: ${ENV(OperatorId)}
  url: /v1/mix/subject/flowTemplates?flowTemplateName=$flowTemplateName&pageNo=$pageNo&pageSize=$pageSize&status=$status
  method: get

