name: 生成流程组信息
variables:
  "repeatValidate": true
  "participants":
    "participantName": "13213094836"
    "participantMobile": "王真真"
request:
  headers:
    cookie: $Cookie
    Content-Type: application/json
    X-Tsign-Open-Tenant-Id: ${ENV(TenantId2)}
    X-Tsign-Open-Operator-Id: ${ENV(OperatorId1)}
    X-Tsign-Mix-Scene: ${ENV(Mix-Scene)}
    X-Tsign-Service-Group: ${ENV(group)}
  url: /v3/mix/scene/processGroup/init
  method: post
  json:
    {
      "participants":[
      {
        "participantMobile": $participantMobile,
        "participantName": $participantName
      }
      ],
      "repeatValidate":true
    }




