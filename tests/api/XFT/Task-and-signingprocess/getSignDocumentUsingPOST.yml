name: 签署文件下载
base_url: ${ENV(mix_open_url)}
variables:
  appId: ${ENV(appid)}
#  thirdOperatorId: ""
#  thirdOrgId: ""
request:
  headers:
    Content-Type: application/json
    X-Tsign-Open-App-Id: $appId
    X-Tsign-Service-Group: ${ENV(group)}
    X-Tsign-Open-Auth-Mode: simple
  url: /v1/mix/signflows/documents
  method: post
  json: $json
#  json:
#    operatorOid:  $operatorOid
#    orgOid:  $orgOid
#    taskId:  $taskId
#    thirdOperatorId:  $thirdOperatorId
#    thirdOrgId:  $thirdOrgId
