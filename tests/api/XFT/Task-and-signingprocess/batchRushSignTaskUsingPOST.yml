name: 批量催签
base_url: ${ENV(mix_open_url)}
variables:
  appId: ${ENV(appid)}
  operatorOid: "72526616536d40b9864c79cd1954dbd1"
  orgOid: "421612cd9edc4f859ddc0808d5a7e321"
  taskIds: ["668fa63fb07e4013b2d1291a88f5d80b"]
  thirdOperatorId: ""   
  thirdOrgId: ""   
request:
  headers:
    Content-Type: application/json
    X-Tsign-Open-App-Id: $appId
    X-Tsign-Service-Group: ${ENV(group)}
    X-Tsign-Open-Auth-Mode: simple
  url: /v1/mix/rush/sign/task
  method: post
  json:
    operatorOid:  $operatorOid
    orgOid:  $orgOid
    taskIds:  $taskIds
    thirdOperatorId:  $thirdOperatorId
    thirdOrgId:  $thirdOrgId
