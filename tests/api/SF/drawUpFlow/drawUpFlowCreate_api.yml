#拟定合同
variables:
    base_url: ${ENV(sf_host)}
request:
        url: $base_url/v1/sf/drawUpFlow/create
        method: POST
        headers:
            X-Tsign-Open-App-Id: ${ENV(sf_appId)}
            X-Tsign-Open-Auth-Mode: secret
            X-Tsign-Open-App-Secret: ${ENV(sf_secret)}
            Content-Type: application/json
            X-Tsign-Service-Group: ${ENV(group)}
        json:
          {
            "econtractSetUpName": "一个签署流程",
            "frontTempUuid": "61CQ98S41J7C1759UO",
            "attachments": [ ],
            "contractValidity": "",
            "signValidity": "",
            "signFlowName": "一个签署流程",
            "templateFlowId": $templateFlowId,
            "participantList": [
              {
                "inputType": 0,
                "organizationId": "",
                "organizationName": "esigntest雷卫测试企业0002",
                "participantRole": 3,
                "signOrder": 1,
                "signatoryName": "甲方",
                "signatoryType": 1,
                "signerId": "",
                "signerMobile": "***********",
                "signerName": "张浩",
                "signerType": 1,
                "templateSignatoryId": "2496775860737543442",
                "templateFlowId": "2496775860217449777",
                "sealBizType": "ALL"
              }
            ],
            "entityParam": { },
            "entityId2ParamsMap": {
              "20204": {
                "jobTitle": null,
                "userId": "********",
                "defaultFullName": "TEST ACCOUNT承龙",
                "cellPhone": null,
                "firstName": "承龙",
                "lastName": "TEST ACCOUNT"
              }
            },
            "operatorUserId": "tsign1",
            "initBizData": {
              "custSignType": "新签",
              "custContractType": "劳动合同",
              "drawFinishUrl": "https://oa.skechers.cn:22443/spa/cube/index.html#/main/cube/search?customid=118",
              "initiateOrganizationId": "",
              "extendBizData": {
                "custTime": "1",
                "custSubject": "斯凯奇（海南）企业管理有限公司",
                "custTerm": "有固定期限",
                "contractStartTime": "*************",
                "contractEndTime": "*************",
                "disableDrawUpFlowAutoStartSignFlag": 0
              }
            }
          }
