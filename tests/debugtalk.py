# -*- coding: utf-8 -*-#

import importlib
import requests
import hashlib
import os
import time

import random
# from mysqlDB import MysqlDb

env = os.environ['env']
# mysqlDB = MysqlDb()
base_url = os.environ["pre_url"]
plugin_project = os.environ["plugin_project"]
openApi_project = os.environ["openApi_project"]
open_project = os.environ["open_project"]
pre_url = os.environ["pre_url"]
group = os.environ['group']

login_url = os.environ["login_url"]
url = login_url + '/account-webserver/login/commit/user'
username = os.environ["username"]
password = os.environ["password"]
appid = os.environ["appid"]
oid = os.environ["oid"]

environmentType = os.environ["environmentType"]
XFT_baseUrl = os.environ["XFT_baseUrl"]
user = os.environ["user"]
psw = os.environ["psw"]

# dataprovider = importlib.import_module("tools.dataprovider")

# def get_testdatamap(filename, key, env):
#     return dataprovider.get_testdatamap(filename, key, env)
def sleep(response, n_secs):
    if response.status_code == 200:  # 接口请求code等于200 则等待n_secs 秒
        time.sleep(n_secs)
    else:  # 接口请求code不等于200 则等待0.5 秒
        time.sleep(0.5)

def int_to_str(arg):
    return str(arg)


def str_to_int(arg):
    return int(arg)


def get_randomNo():
    return random.randint(100000, 999999)




# 使用md5进行加密
def md5_password(password):
    password = str(password).encode('utf-8')
    md5 = hashlib.md5()
    md5.update(password)
    password = md5.hexdigest()
    return password


def gen_signature(app_secret, httpMethod, accept, content_md5, content_type, date, headers, url):
    imported = importlib.import_module("tools.get_ca_signature")
    return imported.gen_signature(app_secret, httpMethod, accept, content_md5, content_type, date, headers, url)


def gen_signature_header(app_secret, httpMethod, accept, content_md5, content_type, date, headers, url):
    imported = importlib.import_module("tools.get_ca_signature")
    return imported.gen_signature_header(app_secret, httpMethod, accept, content_md5, content_type, date, headers, url)


def get_md5_base64_str(content):
    imported = importlib.import_module("tools.get_ca_signature")
    return imported.get_md5_base64_str(content)


def get_timestamp():
    t = time.time()
    ts = int(round(t*1000))
    return ts


# 切换测试企业
def switchOrgan(cookie):
    url = login_url + '/open/switchOrgan/request'
    headers = {
        "Content-Type": "application/json",
        "cookie": cookie
    }
    body = {
        'organId': oid
    }
    response = requests.post(url, headers=headers, json=body)
    print(response.status_code)
    print(response.json())


def get_token():
    headers = {
        "X-Tsign-Open-App-Id": appid,
        "X-Tsign-Open-Auth-Mode": "simple",
        "Content-Type": "application/json",
        "X-Tsign-Service-GROUP": group
    }
    body = {
        "credentials": md5_password(password),
        "principal": username
    }

    response = requests.post(url, headers=headers, json=body)
    print(url)
    print(response.status_code)
    print(response.json())
    print(response.headers)
    return_token = response.headers["Set-Cookie"]
    print(return_token)
    # 切换企业账号
    switchOrgan(return_token)
    return return_token


def get_cookie():
    '''

        :return: 返回cookie
    '''
    imported = importlib.import_module("Utils.get_cookie")
    toolPath = get_toolPath()
    cookie = imported.get_cookie(toolPath, environmentType, user, psw, XFT_baseUrl)
    print(cookie)
    return cookie


def get_toolPath():
    '''
    :return:     #获取当前路径下的文件和文件夹名称列表
    '''

    ##本地
    # current_path = os.listdir()
    # for x in reversed(current_path):
    #     if x == "driver":
    #         driverPath = x
    #         break
    #     else:
    #         pass
    # driverPath = os.path.join(os.getcwd(), driverPath)
    # ----
    # 服务--工程路径

    main_path = os.getcwd()
    # driver路径
    driverPath = main_path + '/driver/'
    return driverPath


# 读取文件内容
def get_file(path):
    filePath = get_toolPath(path)
    print(filePath)
    file = open(filePath, "rb")
    flie_v = str(file.read(), 'utf-8')
    return flie_v


def comparison(v1, v2, v3):
    flag = 'true'
    if (v1 == v2) or (v1 == v3):
        return flag
    else:
        flag = 'false'
        return flag


def vsDataforId(data, value):
    for i in range(len(data)):
        if (data[i]["name"] == value):
            return data[i]["id"]
            break


# def get_appid():
#     appid = mysqlDB.appid()
#     return appid[0]
#
#
# def dele_wq(tablename, columnname, value):
#     dele_sql = "delete from " + str(tablename) + " where " + str(columnname) + " = " + str(value)
#     mysqlDB.excute(dele_sql, ())
#
#
# def predelete_test_data():
#     imported = importlib.import_module("Utils.read_predelete_testdata")
#     sqls = imported.getDeleteSQL()
#     for delete_sql in sqls:
#         mysqlDB.excute(delete_sql, ())
