# 【V3api】签署接口指定实名认证的证件类型 - 测试用例

## 功能测试

	TL-psnIDCardNum不为空psnIDCardType为空时默认值设置验证
		PD-前置条件：V3签署接口正常可用；准备有效身份证号码；
		步骤一：调用V3创建签署流程接口
		步骤二：传入psnIDCardNum="110101199001011234"
		步骤三：psnIDCardType参数不传或传空值
		步骤四：提交创建签署流程请求
		ER-预期结果
			1：接口调用成功返回200状态码
			2：psnIDCardType自动设置为"CRED_PSN_CH_IDCARD"
			3：签署流程创建成功

	TL-psnIDCardNum为空psnIDCardType不为空时透传验证
		PD-前置条件：V3签署接口正常可用；准备有效证件类型参数；
		步骤一：调用V3创建签署流程接口
		步骤二：psnIDCardNum参数不传或传空值
		步骤三：传入psnIDCardType="CRED_PSN_CH_TWCARD"
		步骤四：提交创建签署流程请求
		ER-预期结果
			1：接口调用成功返回200状态码
			2：psnIDCardType值保持为"CRED_PSN_CH_TWCARD"
			3：签署流程创建成功

	TL-psnIDCardNum不为空psnIDCardType不为空时完整参数验证
		PD-前置条件：V3签署接口正常可用；准备有效身份证号码和证件类型；
		步骤一：调用V3创建签署流程接口
		步骤二：传入psnIDCardNum="110101199001011234"
		步骤三：传入psnIDCardType="CRED_PSN_CH_IDCARD"
		步骤四：提交创建签署流程请求
		ER-预期结果
			1：接口调用成功返回200状态码
			2：参数值保持不变
			3：签署流程创建成功

	TL-证件类型透传到实名页面验证
		PD-前置条件：签署流程已创建且psnIDCardType="CRED_PSN_CH_TWCARD"；签署人已收到签署邀请；
		步骤一：签署人点击签署链接进入签署页面
		步骤二：触发实名认证流程
		步骤三：查看实名认证页面证件类型显示
		步骤四：验证证件类型字段是否可修改
		ER-预期结果
			1：实名页面正确显示"台湾来往大陆通行证"
			2：证件类型字段为只读状态不可修改
			3：页面提示用户输入对应证件号码

	TL-实名认证完成后签署流程验证
		PD-前置条件：实名页面已显示指定证件类型；准备匹配的证件号码；
		步骤一：在实名页面输入对应证件号码
		步骤二：完成其他必要的实名认证步骤
		步骤三：提交实名认证
		步骤四：继续完成签署操作
		ER-预期结果
			1：实名认证成功通过
			2：可以正常进入签署环节
			3：整个签署流程顺利完成

	TL-批量签署certType一致时身份识别验证
		PD-前置条件：同一签署人有多个待签署流程；所有流程certType都为"CRED_PSN_CH_IDCARD"；
		步骤一：签署人登录系统查看待签署列表
		步骤二：选择多个待签署流程
		步骤三：点击批量签署按钮
		步骤四：系统进行身份识别验证
		ER-预期结果
			1：系统正确识别为同一签署人
			2：批量签署功能正常启用
			3：可以统一完成实名认证和签署

	TL-批量签署certType不一致时身份识别阻止验证
		PD-前置条件：同一签署人有多个待签署流程；流程A的certType为空，流程B的certType为"CRED_PSN_CH_TWCARD"；
		步骤一：签署人登录系统查看待签署列表
		步骤二：选择包含不同certType的多个流程
		步骤三：点击批量签署按钮
		步骤四：系统进行身份识别验证
		ER-预期结果
			1：系统判定为不同签署人
			2：批量签署功能被阻止
			3：返回明确的错误提示信息

## 边界测试

	TL-psnIDCardNum和psnIDCardType都为空时处理验证
		PD-前置条件：V3签署接口正常可用；
		步骤一：调用V3创建签署流程接口
		步骤二：psnIDCardNum和psnIDCardType都不传或传空值
		步骤三：提交创建签署流程请求
		步骤四：查看接口返回结果
		ER-预期结果
			1：接口按既定业务规则处理
			2：返回明确的处理结果
			3：不影响其他功能正常使用

	TL-身份证号码长度边界值验证
		PD-前置条件：V3签署接口正常可用；
		步骤一：测试17位身份证号码输入
		步骤二：测试19位身份证号码输入
		步骤三：测试标准18位身份证号码输入
		步骤四：分别提交请求验证结果
		ER-预期结果
			1：18位标准号码正常处理
			2：非18位号码返回格式错误
			3：错误信息明确指出格式要求

	TL-证件类型字段长度边界值验证
		PD-前置条件：V3签署接口正常可用；
		步骤一：传入超长的certType值
		步骤二：传入空字符串的certType值
		步骤三：传入特殊字符的certType值
		步骤四：分别提交请求验证结果
		ER-预期结果
			1：超长值被截断或返回错误
			2：空字符串按空值处理
			3：特殊字符被正确过滤或拒绝

## 异常测试

	TL-不支持的证件类型异常处理验证
		PD-前置条件：V3签署接口正常可用；
		步骤一：调用V3创建签署流程接口
		步骤二：传入不支持的证件类型"INVALID_CERT_TYPE"
		步骤三：提交创建签署流程请求
		步骤四：查看接口返回的错误信息
		ER-预期结果
			1：接口返回参数错误状态码
			2：错误信息明确指出不支持的证件类型
			3：提供支持的证件类型列表

	TL-证件号码格式错误异常处理验证
		PD-前置条件：V3签署接口正常可用；
		步骤一：调用V3创建签署流程接口
		步骤二：传入格式错误的psnIDCardNum="123456"
		步骤三：提交创建签署流程请求
		步骤四：查看接口返回的错误信息
		ER-预期结果
			1：接口返回参数格式错误
			2：错误信息明确指出身份证号码格式不正确
			3：提供正确的格式示例

	TL-证件类型与证件号码不匹配异常处理验证
		PD-前置条件：签署流程中psnIDCardType="CRED_PSN_CH_TWCARD"；进入实名认证页面；
		步骤一：在实名页面输入身份证号码格式的证件号
		步骤二：提交实名认证请求
		步骤三：查看系统返回的错误信息
		步骤四：验证用户是否可以重新输入
		ER-预期结果
			1：实名认证失败
			2：返回证件类型与号码不匹配的错误提示
			3：允许用户重新输入正确格式的证件号码

## 性能测试

	TL-V3接口响应时间性能验证
		PD-前置条件：V3签署接口正常可用；准备性能测试工具；
		步骤一：发送标准的创建签署流程请求
		步骤二：记录接口响应时间
		步骤三：重复测试100次取平均值
		步骤四：分析响应时间分布情况
		ER-预期结果
			1：平均响应时间≤2秒
			2：95%请求响应时间≤3秒
			3：无超时或异常响应

	TL-并发创建签署流程性能验证
		PD-前置条件：V3签署接口正常可用；准备并发测试工具；
		步骤一：同时发送100个创建签署流程请求
		步骤二：每个请求包含不同的certType参数
		步骤三：记录成功率和响应时间
		步骤四：分析系统资源使用情况
		ER-预期结果
			1：请求成功率≥99%
			2：平均响应时间≤5秒
			3：系统资源使用在正常范围内

	TL-批量签署处理性能验证
		PD-前置条件：创建100个签署流程；所有流程使用相同签署人和certType；
		步骤一：执行批量签署操作
		步骤二：记录身份识别处理时间
		步骤三：记录整体批量处理时间
		步骤四：验证处理成功率
		ER-预期结果
			1：身份识别处理时间≤1秒
			2：批量处理成功率≥99%
			3：整体处理时间在可接受范围内

## 安全测试

	TL-个人用户修改certType权限验证
		PD-前置条件：个人用户账号；已创建的签署流程；
		步骤一：个人用户登录系统
		步骤二：尝试修改签署流程中的certType字段
		步骤三：提交修改请求
		步骤四：查看系统返回结果
		ER-预期结果
			1：系统拒绝修改操作
			2：返回权限不足的错误提示
			3：原certType值保持不变

	TL-SQL注入攻击防护验证
		PD-前置条件：V3签署接口正常可用；
		步骤一：在psnIDCardNum参数中注入SQL语句
		步骤二：在psnIDCardType参数中注入SQL语句
		步骤三：提交恶意请求
		步骤四：查看系统响应和日志
		ER-预期结果
			1：系统正确处理恶意输入
			2：不发生SQL注入攻击
			3：返回参数格式错误而非系统错误

	TL-参数篡改防护验证
		PD-前置条件：正常的签署流程请求；网络抓包工具；
		步骤一：拦截正常的签署流程创建请求
		步骤二：修改请求中的certType参数值
		步骤三：重放篡改后的请求
		步骤四：查看系统处理结果
		ER-预期结果
			1：系统检测到参数篡改
			2：拒绝处理篡改请求
			3：记录安全审计日志

## 兼容性测试

	TL-PC端功能兼容性验证
		PD-前置条件：PC端浏览器环境；V3签署接口正常可用；
		步骤一：在PC端创建包含certType的签署流程
		步骤二：在PC端进入实名认证页面
		步骤三：验证证件类型显示和交互
		步骤四：完成整个签署流程
		ER-预期结果
			1：PC端功能完全正常
			2：证件类型正确显示且不可修改
			3：用户体验良好

	TL-H5端功能兼容性验证
		PD-前置条件：H5移动端环境；V3签署接口正常可用；
		步骤一：在H5端创建包含certType的签署流程
		步骤二：在H5端进入实名认证页面
		步骤三：验证证件类型显示和交互
		步骤四：完成整个签署流程
		ER-预期结果
			1：H5端功能完全正常
			2：证件类型在移动端正确显示
			3：触摸交互体验良好

	TL-不同浏览器兼容性验证
		PD-前置条件：Chrome、Firefox、Safari、Edge浏览器；
		步骤一：分别在不同浏览器中测试功能
		步骤二：验证证件类型透传功能
		步骤三：验证实名认证页面显示
		步骤四：对比各浏览器表现差异
		ER-预期结果
			1：所有主流浏览器功能正常
			2：证件类型显示一致
			3：无明显兼容性问题

## 补充测试用例

	TL-多种证件类型支持详细验证
		PD-前置条件：V3签署接口正常可用；准备多种证件类型参数；
		步骤一：分别测试CRED_PSN_CH_IDCARD身份证类型
		步骤二：分别测试CRED_PSN_CH_TWCARD台湾通行证类型
		步骤三：分别测试其他支持的证件类型
		步骤四：验证每种类型在实名页面的显示
		ER-预期结果
			1：所有支持的证件类型都能正确处理
			2：实名页面显示对应的证件类型名称
			3：每种类型都能完成完整的实名认证流程

	TL-实名认证中断恢复场景验证
		PD-前置条件：签署流程进行到实名认证环节；证件类型已透传显示；
		步骤一：在实名认证过程中关闭浏览器或断网
		步骤二：重新打开浏览器并进入签署流程
		步骤三：查看实名认证页面状态
		步骤四：继续完成实名认证
		ER-预期结果
			1：证件类型设置得到保持
			2：可以从中断处继续实名认证
			3：不需要重新设置证件类型

	TL-大数据量批量签署场景验证
		PD-前置条件：创建1000个签署流程；所有流程使用相同签署人和certType；
		步骤一：签署人登录系统查看大量待签署流程
		步骤二：选择所有1000个流程进行批量签署
		步骤三：系统进行身份识别和批量处理
		步骤四：监控处理过程和结果
		ER-预期结果
			1：系统能正确处理大数据量批量签署
			2：身份识别准确率100%
			3：批量处理成功率≥99%

## 冒烟测试用例

	MYTL-psnIDCardNum不为空psnIDCardType为空时默认值设置验证
		PD-前置条件：V3签署接口正常可用；准备有效身份证号码；
		步骤一：调用V3创建签署流程接口
		步骤二：传入psnIDCardNum="110101199001011234"
		步骤三：psnIDCardType参数不传或传空值
		步骤四：提交创建签署流程请求
		ER-预期结果
			1：接口调用成功返回200状态码
			2：psnIDCardType自动设置为"CRED_PSN_CH_IDCARD"
			3：签署流程创建成功

	MYTL-psnIDCardNum为空psnIDCardType不为空时透传验证
		PD-前置条件：V3签署接口正常可用；准备有效证件类型参数；
		步骤一：调用V3创建签署流程接口
		步骤二：psnIDCardNum参数不传或传空值
		步骤三：传入psnIDCardType="CRED_PSN_CH_TWCARD"
		步骤四：提交创建签署流程请求
		ER-预期结果
			1：接口调用成功返回200状态码
			2：psnIDCardType值保持为"CRED_PSN_CH_TWCARD"
			3：签署流程创建成功

	MYTL-证件类型透传到实名页面验证
		PD-前置条件：签署流程已创建且psnIDCardType="CRED_PSN_CH_TWCARD"；签署人已收到签署邀请；
		步骤一：签署人点击签署链接进入签署页面
		步骤二：触发实名认证流程
		步骤三：查看实名认证页面证件类型显示
		步骤四：验证证件类型字段是否可修改
		ER-预期结果
			1：实名页面正确显示"台湾来往大陆通行证"
			2：证件类型字段为只读状态不可修改
			3：页面提示用户输入对应证件号码

	MYTL-实名认证完成后签署流程验证
		PD-前置条件：实名页面已显示指定证件类型；准备匹配的证件号码；
		步骤一：在实名页面输入对应证件号码
		步骤二：完成其他必要的实名认证步骤
		步骤三：提交实名认证
		步骤四：继续完成签署操作
		ER-预期结果
			1：实名认证成功通过
			2：可以正常进入签署环节
			3：整个签署流程顺利完成

	MYTL-批量签署certType一致时身份识别验证
		PD-前置条件：同一签署人有多个待签署流程；所有流程certType都为"CRED_PSN_CH_IDCARD"；
		步骤一：签署人登录系统查看待签署列表
		步骤二：选择多个待签署流程
		步骤三：点击批量签署按钮
		步骤四：系统进行身份识别验证
		ER-预期结果
			1：系统正确识别为同一签署人
			2：批量签署功能正常启用
			3：可以统一完成实名认证和签署

	MYTL-批量签署certType不一致时身份识别阻止验证
		PD-前置条件：同一签署人有多个待签署流程；流程A的certType为空，流程B的certType为"CRED_PSN_CH_TWCARD"；
		步骤一：签署人登录系统查看待签署列表
		步骤二：选择包含不同certType的多个流程
		步骤三：点击批量签署按钮
		步骤四：系统进行身份识别验证
		ER-预期结果
			1：系统判定为不同签署人
			2：批量签署功能被阻止
			3：返回明确的错误提示信息

	MYTL-V3接口响应时间性能验证
		PD-前置条件：V3签署接口正常可用；准备性能测试工具；
		步骤一：发送标准的创建签署流程请求
		步骤二：记录接口响应时间
		步骤三：重复测试100次取平均值
		步骤四：分析响应时间分布情况
		ER-预期结果
			1：平均响应时间≤2秒
			2：95%请求响应时间≤3秒
			3：无超时或异常响应
