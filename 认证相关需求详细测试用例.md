# 认证相关需求详细测试用例

## 需求1：【认证】核身接口增加传参控制是否展示协议

### 功能测试

#### 个人核身网页接口协议控制

##### TL-个人核身网页接口showAgreement为true时协议完整展示验证

###### PD-前置条件：核身服务正常；协议服务可用；

###### 步骤一：调用个人核身网页接口/identity_service/sg2nty

###### 步骤二：请求参数中设置showAgreement=true

###### 步骤三：提交核身请求

###### 步骤四：检查返回的核身页面内容

###### 步骤五：验证页面中协议展示情况

###### 步骤六：检查短信中协议链接

###### ER-预期结果：1：接口调用成功返回200状态码；2：核身页面正常渲染；3：页面显示数字证书协议、服务协议和隐私政策；4：协议链接可点击访问；5：短信验证码中包含协议链接；6：协议展示优先级高于签署侧校验；

##### TL-个人核身网页接口showAgreement为false时协议隐藏验证

###### PD-前置条件：核身服务正常；协议服务可用；

###### 步骤一：调用个人核身网页接口/identity_service/sg2nty

###### 步骤二：请求参数中设置showAgreement=false

###### 步骤三：提交核身请求

###### 步骤四：检查返回的核身页面内容

###### 步骤五：验证页面中协议展示情况

###### 步骤六：检查短信内容

###### ER-预期结果：1：接口调用成功返回200状态码；2：核身页面正常渲染；3：页面中不显示任何协议内容；4：短信验证码中不包含协议链接；5：核身功能正常可用；

##### TL-个人核身网页接口showAgreement参数缺省时默认值验证

###### PD-前置条件：核身服务正常；

###### 步骤一：调用个人核身网页接口/identity_service/sg2nty

###### 步骤二：请求参数中不传showAgreement参数

###### 步骤三：提交核身请求

###### 步骤四：检查返回的核身页面内容

###### 步骤五：验证系统默认行为

###### ER-预期结果：1：接口调用成功；2：系统默认showAgreement为false；3：页面中不显示协议内容；4：短信中不包含协议链接；5：核身流程正常进行；

#### 个人核身扫脸api接口协议控制

##### TL-个人核身扫脸api接口showAgreement参数功能验证

###### PD-前置条件：扫脸服务正常；摄像头权限已授权；

###### 步骤一：调用个人核身扫脸api接口/identity_service/wbsb6y

###### 步骤二：请求参数中设置showAgreement=true

###### 步骤三：启动扫脸核身流程

###### 步骤四：检查扫脸页面协议展示

###### 步骤五：完成扫脸认证流程

###### 步骤六：验证协议展示不影响扫脸功能

###### ER-预期结果：1：扫脸接口调用成功；2：扫脸页面正常加载；3：页面中显示相关协议内容；4：协议展示不干扰扫脸操作；5：扫脸认证功能正常；6：协议优先级高于签署侧校验；

##### TL-个人核身扫脸api接口不同认证用途协议展示验证

###### PD-前置条件：扫脸服务正常；

###### 步骤一：调用个人核身扫脸api接口

###### 步骤二：设置showAgreement=true

###### 步骤三：设置认证用途为企业相关场景

###### 步骤四：启动扫脸核身流程

###### 步骤五：检查协议展示内容

###### ER-预期结果：1：扫脸接口调用成功；2：根据认证用途展示企业证书协议；3：协议内容与认证用途匹配；4：扫脸功能正常；

#### 个人核身三要素api接口协议控制

##### TL-个人核身三要素api接口showAgreement参数验证

###### PD-前置条件：三要素验证服务正常；

###### 步骤一：调用个人核身三要素api接口/identity_service/wry7rc

###### 步骤二：设置showAgreement=true

###### 步骤三：输入姓名、身份证号、手机号三要素信息

###### 步骤四：提交三要素验证请求

###### 步骤五：检查验证页面协议展示

###### 步骤六：完成三要素验证流程

###### ER-预期结果：1：三要素接口调用成功；2：验证页面正常展示；3：页面中显示协议内容；4：三要素验证功能正常；5：协议展示不影响验证流程；

##### TL-个人核身三要素api接口协议与验证流程独立性验证

###### PD-前置条件：三要素验证服务正常；协议服务异常；

###### 步骤一：调用个人核身三要素api接口

###### 步骤二：设置showAgreement=true

###### 步骤三：输入有效的三要素信息

###### 步骤四：提交验证请求

###### 步骤五：验证核身功能是否受协议服务影响

###### ER-预期结果：1：三要素验证正常进行；2：协议展示失败不影响核身功能；3：验证结果准确；4：系统记录协议服务异常；

#### 个人核身四要素api接口协议控制

##### TL-个人核身四要素api接口showAgreement参数完整验证

###### PD-前置条件：四要素验证服务正常；银行卡验证服务可用；

###### 步骤一：调用个人核身四要素api接口/identity_service/tgp6t3

###### 步骤二：设置showAgreement=true

###### 步骤三：输入姓名、身份证号、手机号、银行卡号四要素信息

###### 步骤四：提交四要素验证请求

###### 步骤五：检查验证页面协议展示

###### 步骤六：完成四要素验证流程

###### 步骤七：验证企业证书协议展示逻辑

###### ER-预期结果：1：四要素接口调用成功；2：验证页面正常展示；3：页面中显示完整协议内容；4：根据认证用途正确展示企业证书协议；5：四要素验证功能正常；6：银行卡验证通过；

### 场景测试

#### 多接口协议展示一致性场景

##### TL-四个核身接口协议展示一致性验证

###### PD-前置条件：所有核身服务正常；协议服务可用；

###### 步骤一：分别调用四个核身接口（网页、扫脸、三要素、四要素）

###### 步骤二：所有接口均设置showAgreement=true

###### 步骤三：检查各接口返回的协议内容

###### 步骤四：对比协议展示的一致性

###### 步骤五：验证协议链接的有效性

###### ER-预期结果：1：所有接口调用成功；2：协议内容在各接口中保持一致；3：协议链接均可正常访问；4：协议展示格式统一；5：用户体验一致；

##### TL-协议展示与签署侧校验优先级验证场景

###### PD-前置条件：核身服务正常；签署服务正常；存在签署侧协议校验配置；

###### 步骤一：配置签署侧协议校验为不展示

###### 步骤二：调用核身接口设置showAgreement=true

###### 步骤三：提交核身请求

###### 步骤四：检查协议展示结果

###### 步骤五：验证参数优先级生效

###### ER-预期结果：1：核身接口调用成功；2：协议正常展示；3：showAgreement参数优先级高于签署侧校验；4：签署侧配置被覆盖；5：协议展示符合预期；

#### 协议展示与核身流程集成场景

##### TL-协议展示不影响核身成功率验证场景

###### PD-前置条件：核身服务正常；协议服务正常；

###### 步骤一：准备100个有效的核身测试数据

###### 步骤二：50个请求设置showAgreement=true

###### 步骤三：50个请求设置showAgreement=false

###### 步骤四：并发执行核身请求

###### 步骤五：统计核身成功率

###### 步骤六：对比两组数据的成功率差异

###### ER-预期结果：1：所有请求正常处理；2：两组核身成功率基本一致；3：协议展示不影响核身功能；4：响应时间差异在可接受范围内；

## 需求2：V3账号解绑链接支持微信小程序redirectUrl

### 功能测试

#### 微信小程序redirectUrl支持

##### TL-V3账号解绑接口支持wechat://back参数验证

###### PD-前置条件：V3账号服务正常；微信小程序环境；账号已绑定状态；

###### 步骤一：在微信小程序中调用V3账号解绑接口

###### 步骤二：设置redirectUrl参数为"wechat://back"

###### 步骤三：执行账号解绑操作

###### 步骤四：确认解绑操作成功

###### 步骤五：检查页面跳转行为

###### 步骤六：验证微信back方法调用

###### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：解绑状态更新正确；4：成功唤起微信back方法；5：用户返回到上一页面；6：跳转过程流畅无卡顿；

##### TL-V3账号解绑接口普通redirectUrl参数兼容性验证

###### PD-前置条件：V3账号服务正常；账号已绑定状态；

###### 步骤一：调用V3账号解绑接口

###### 步骤二：设置redirectUrl参数为普通HTTP URL

###### 步骤三：执行账号解绑操作

###### 步骤四：确认解绑操作成功

###### 步骤五：检查页面跳转结果

###### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：页面正确跳转到指定URL；4：原有跳转功能保持兼容；5：不影响非微信小程序场景；

##### TL-V3账号解绑接口redirectUrl参数为空时默认行为验证

###### PD-前置条件：V3账号服务正常；账号已绑定状态；

###### 步骤一：调用V3账号解绑接口

###### 步骤二：不传redirectUrl参数或传空值

###### 步骤三：执行账号解绑操作

###### 步骤四：确认解绑操作成功

###### 步骤五：检查系统默认跳转行为

###### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：使用系统默认跳转逻辑；4：不影响解绑核心功能；5：向后兼容性良好；

### 场景测试

#### 微信小程序环境适配场景

##### TL-微信小程序环境识别与处理验证场景

###### PD-前置条件：V3账号服务正常；

###### 步骤一：在微信小程序环境中调用解绑接口

###### 步骤二：设置redirectUrl为"wechat://back"

###### 步骤三：在非微信小程序环境中调用解绑接口

###### 步骤四：设置相同的redirectUrl参数

###### 步骤五：对比两种环境的处理结果

###### ER-预期结果：1：系统能正确识别运行环境；2：微信小程序环境正确处理wechat://back；3：非微信环境使用降级处理；4：两种环境解绑功能均正常；5：用户体验适配良好；

#### 解绑流程完整性场景

##### TL-账号解绑完整流程端到端验证场景

###### PD-前置条件：V3账号服务正常；微信小程序环境；多个账号绑定关系；

###### 步骤一：查询当前账号绑定状态

###### 步骤二：选择需要解绑的账号

###### 步骤三：调用解绑接口设置redirectUrl为"wechat://back"

###### 步骤四：确认解绑操作

###### 步骤五：验证解绑后的账号状态

###### 步骤六：检查相关业务功能影响

###### 步骤七：验证页面跳转和用户体验

###### ER-预期结果：1：绑定状态查询准确；2：解绑操作执行成功；3：账号状态正确更新；4：相关业务功能正常调整；5：页面跳转流畅；6：用户操作体验良好；7：数据一致性保持；

## 需求3：国际短信扣费子产品上游适配

### 功能测试

#### 国内外短信扣费区分

##### TL-国内手机号发送意愿认证短信扣费验证

###### PD-前置条件：短信服务正常；扣费系统正常；客户账户余额充足；

###### 步骤一：使用国内手机号（如13812345678）发起意愿认证

###### 步骤二：调用/v1/willingness/sys_transId/createCodeAuth接口

###### 步骤三：触发短信验证码发送

###### 步骤四：检查短信发送状态

###### 步骤五：查询扣费记录和金额

###### 步骤六：验证扣费子产品类型

###### ER-预期结果：1：短信发送成功；2：用户收到验证码短信；3：按国内短信标准扣费；4：扣费记录准确完整；5：扣费金额符合国内短信定价；6：扣费子产品为普通短信产品；

##### TL-国际手机号发送意愿认证短信扣费验证

###### PD-前置条件：短信服务正常；扣费系统正常；客户账户余额充足；国际短信子产品已开通；

###### 步骤一：使用国际手机号（如+1234567890）发起意愿认证

###### 步骤二：调用/v1/willingness/sys_transId/createCodeAuth接口

###### 步骤三：触发短信验证码发送

###### 步骤四：检查短信发送状态

###### 步骤五：查询扣费记录和金额

###### 步骤六：验证扣费子产品code

###### ER-预期结果：1：短信发送成功；2：用户收到验证码短信；3：按国际短信标准扣费；4：扣费金额为国内短信的十几二十倍；5：扣费子产品code为service-C-127；6：扣费记录标注为国际短信；

##### TL-手机号归属地识别准确性验证

###### PD-前置条件：短信服务正常；手机号归属地识别服务正常；

###### 步骤一：准备不同国家和地区的手机号样本

###### 步骤二：分别调用意愿认证短信接口

###### 步骤三：系统识别手机号归属地

###### 步骤四：检查扣费标准应用

###### 步骤五：验证识别结果准确性

###### ER-预期结果：1：系统正确识别手机号归属地；2：国内手机号按国内标准扣费；3：国际手机号按国际标准扣费；4：港澳台手机号按相应标准处理；5：识别准确率达到99%以上；

#### 客户白名单控制机制

##### TL-存量客户白名单控制国际短信扣费验证

###### PD-前置条件：短信服务正常；客户在白名单中；扣费系统正常；

###### 步骤一：确认客户在国际短信白名单中

###### 步骤二：使用国际手机号发送意愿认证短信

###### 步骤三：调用短信发送接口

###### 步骤四：检查扣费处理逻辑

###### 步骤五：验证扣费金额和标准

###### ER-预期结果：1：短信发送成功；2：按普通短信费用扣费；3：不按国际短信标准收费；4：白名单控制机制生效；5：扣费记录标注白名单处理；

##### TL-非白名单客户国际短信扣费验证

###### PD-前置条件：短信服务正常；客户不在白名单中；扣费系统正常；

###### 步骤一：确认客户不在国际短信白名单中

###### 步骤二：使用国际手机号发送意愿认证短信

###### 步骤三：调用短信发送接口

###### 步骤四：检查扣费处理逻辑

###### 步骤五：验证扣费金额和标准

###### ER-预期结果：1：短信发送成功；2：按国际短信费用扣费；3：扣费金额较高；4：默认收费逻辑生效；5：扣费子产品code为service-C-127；

##### TL-白名单配置动态生效验证

###### PD-前置条件：短信服务正常；扣费系统正常；白名单管理功能正常；

###### 步骤一：客户初始不在白名单中

###### 步骤二：发送国际短信验证扣费标准

###### 步骤三：将客户添加到白名单

###### 步骤四：再次发送国际短信

###### 步骤五：对比两次扣费结果

###### 步骤六：验证白名单配置实时生效

###### ER-预期结果：1：添加白名单前按国际标准扣费；2：添加白名单后按普通标准扣费；3：白名单配置实时生效；4：扣费切换准确无误；5：系统记录配置变更日志；

### 场景测试

#### 扣费子产品完整性场景

##### TL-国际短信子产品开通状态检查场景

###### PD-前置条件：客户管理系统正常；扣费系统正常；

###### 步骤一：检查客户是否开通国际短信子产品

###### 步骤二：未开通客户使用国际手机号发送短信

###### 步骤三：已开通客户使用国际手机号发送短信

###### 步骤四：对比两种情况的处理结果

###### 步骤五：验证子产品开通必要性

###### ER-预期结果：1：系统正确检查子产品开通状态；2：未开通客户无法使用国际短信或提示开通；3：已开通客户正常使用并扣费；4：子产品控制机制有效；5：用户收到明确的状态提示；

#### 成本覆盖验证场景

##### TL-国际短信成本覆盖率验证场景

###### PD-前置条件：短信服务正常；成本核算系统正常；

###### 步骤一：发送100条国际短信到不同国家

###### 步骤二：记录实际发送成本

###### 步骤三：计算客户扣费总额

###### 步骤四：分析成本覆盖率

###### 步骤五：验证定价合理性

###### ER-预期结果：1：国际短信全部发送成功；2：实际成本准确记录；3：客户扣费计算正确；4：成本覆盖率达到预期目标；5：定价策略合理有效；

## 需求4：企业法人意愿认证方式调整

### 功能测试

#### 认证方式调整

##### TL-企业法人刷脸加组织机构四要素认证流程验证

###### PD-前置条件：v2版本企业实名服务正常；法人刷脸服务可用；组织机构验证服务正常；

###### 步骤一：访问v2版本企业实名页面/v2/identity/auth/web/organization/{flowId}/infoVerify

###### 步骤二：选择企业法人认证方式

###### 步骤三：进行法人本人刷脸认证

###### 步骤四：刷脸认证通过后进入四要素验证

###### 步骤五：输入组织机构名称、统一社会信用代码、法人姓名、法人身份证号

###### 步骤六：提交四要素验证请求

###### 步骤七：完成整个认证流程

###### ER-预期结果：1：企业实名页面正常加载；2：法人刷脸认证成功；3：组织机构四要素信息比对通过；4：认证流程顺畅完整；5：认证结果准确可靠；6：认证方式记录为"法人刷脸+组织机构四要素信息比对"；

##### TL-认证方式显示调整验证

###### PD-前置条件：企业已完成新认证方式；运营支撑平台正常；

###### 步骤一：企业完成法人刷脸+四要素认证

###### 步骤二：登录运营支撑平台

###### 步骤三：查看企业实名认证详情

###### 步骤四：检查认证管理子流程类型显示

###### 步骤五：验证认证方式描述准确性

###### ER-预期结果：1：认证详情页面正常显示；2：子流程类型显示为"法人刷脸+组织机构四要素信息比对"；3：不再显示"组织机构三要素信息比对"；4：认证信息描述准确严谨；5：历史认证记录正确更新；

##### TL-存证出证信息调整验证

###### PD-前置条件：企业已完成新认证方式；存证服务正常；出证服务可用；

###### 步骤一：触发企业认证存证流程

###### 步骤二：生成企业认证证书

###### 步骤三：检查证书中认证方式描述

###### 步骤四：验证证书法律效力相关信息

###### 步骤五：对比调整前后证书内容差异

###### ER-预期结果：1：存证流程正常执行；2：认证证书成功生成；3：证书中认证方式描述为"法人刷脸+组织机构四要素信息比对"；4：证书具有完整法律效力；5：认证信息更加严谨可信；6：证书内容符合监管要求；

### 场景测试

#### 认证流程完整性场景

##### TL-企业法人认证完整业务流程验证场景

###### PD-前置条件：企业账户正常；法人信息完整；相关认证服务正常；

###### 步骤一：企业发起实名认证申请

###### 步骤二：选择法人本人认证方式

###### 步骤三：法人进行刷脸认证

###### 步骤四：系统验证法人身份信息

###### 步骤五：进入组织机构四要素验证环节

###### 步骤六：输入完整的企业四要素信息

###### 步骤七：系统进行四要素信息比对

###### 步骤八：认证通过后更新企业状态

###### 步骤九：生成认证证书和相关凭证

###### 步骤十：通知企业认证完成

###### ER-预期结果：1：认证申请提交成功；2：法人刷脸认证通过；3：四要素信息比对成功；4：企业认证状态正确更新；5：认证证书内容准确；6：企业收到认证完成通知；7：整个流程用户体验良好；8：认证结果具有法律效力；

#### 认证方式一致性场景

##### TL-多平台认证方式显示一致性验证场景

###### PD-前置条件：企业已完成新认证方式；多个相关系统正常；

###### 步骤一：在运营支撑平台查看认证详情

###### 步骤二：在存证系统查看认证记录

###### 步骤三：在出证系统查看证书内容

###### 步骤四：在客户端查看认证状态

###### 步骤五：对比各平台认证方式显示

###### 步骤六：验证信息一致性

###### ER-预期结果：1：各平台认证信息显示一致；2：认证方式描述统一为"法人刷脸+组织机构四要素信息比对"；3：认证时间和状态信息同步；4：不存在信息不一致问题；5：用户在各平台看到的信息一致；

## 需求5：SSO登录支持人机校验

### 功能测试

#### 人机校验接口集成

##### TL-SSO登录人机校验接口调用验证

###### PD-前置条件：SSO服务正常；极验服务可用；

###### 步骤一：访问SSO登录页面

###### 步骤二：调用人机校验接口/account-webserver/sender/robotAuth/apply/v3

###### 步骤三：获取极验参数（challenge、gt等）

###### 步骤四：检查返回参数格式和内容

###### 步骤五：验证参数有效性

###### ER-预期结果：1：人机校验接口调用成功；2：返回code为0表示成功；3：data中包含success、newCaptcha、challenge、gt参数；4：challenge和gt参数格式正确；5：参数可用于后续极验流程；

##### TL-SSO发送验证码接口极验参数校验

###### PD-前置条件：SSO服务正常；极验服务正常；已获取有效极验参数；

###### 步骤一：完成人机校验获取校验结果

###### 步骤二：调用发送验证码接口/account-webserver/login/sso/account/bindAccount/send

###### 步骤三：在请求中传入credentials和type参数

###### 步骤四：在data.robotModel中传入geetest_challenge、geetest_validate、geetest_seccode

###### 步骤五：提交请求并检查响应

###### ER-预期结果：1：发送验证码接口调用成功；2：极验参数校验通过；3：验证码发送成功；4：返回authCodeId任务ID；5：用户收到验证码短信；

##### TL-SSO绑定登录接口流程验证

###### PD-前置条件：SSO服务正常；已完成人机校验和验证码发送；

###### 步骤一：用户输入收到的验证码

###### 步骤二：调用绑定三方账号登录接口/login/sso/account/bindAccount/check

###### 步骤三：提交验证码和相关参数

###### 步骤四：完成账号绑定和登录

###### 步骤五：验证登录状态和权限

###### ER-预期结果：1：绑定登录接口调用成功；2：验证码校验通过；3：账号绑定成功；4：用户登录状态正确；5：获得相应系统权限；

#### 极验降级处理

##### TL-极验服务异常时降级处理验证

###### PD-前置条件：SSO服务正常；极验服务异常或不可用；

###### 步骤一：访问SSO登录页面

###### 步骤二：尝试调用人机校验接口

###### 步骤三：检测到极验服务异常

###### 步骤四：系统启动降级模式

###### 步骤五：直接调用发送验证码接口（不传极验参数）

###### 步骤六：完成登录流程

###### ER-预期结果：1：系统检测到极验服务异常；2：自动启用降级模式；3：不展示极验组件；4：后端不进行极验信息校验；5：登录流程正常进行；6：用户无感知切换；

##### TL-极验降级配置动态生效验证

###### PD-前置条件：SSO服务正常；极验降级配置可动态调整；

###### 步骤一：配置系统启用极验功能

###### 步骤二：用户进行正常的人机校验登录

###### 步骤三：动态配置系统关闭极验功能

###### 步骤四：新用户访问登录页面

###### 步骤五：验证降级处理效果

###### ER-预期结果：1：配置变更实时生效；2：启用时正常展示极验；3：关闭后自动降级；4：不影响正在进行的登录流程；5：新用户按降级模式处理；

#### 多端适配验证

##### TL-PC端SSO登录人机校验功能验证

###### PD-前置条件：PC浏览器环境；SSO服务正常；极验服务正常；

###### 步骤一：在PC端访问SSO登录页面

###### 步骤二：加载极验人机校验组件

###### 步骤三：完成滑动验证或点击验证

###### 步骤四：获取校验结果参数

###### 步骤五：继续完成登录流程

###### ER-预期结果：1：PC端极验组件正常加载；2：鼠标交互体验良好；3：校验成功率高；4：参数获取准确；5：登录流程顺畅；

##### TL-H5端SSO登录人机校验功能验证

###### PD-前置条件：移动端浏览器环境；SSO服务正常；极验服务正常；

###### 步骤一：在H5端访问SSO登录页面

###### 步骤二：加载极验人机校验组件

###### 步骤三：完成触摸滑动或点击验证

###### 步骤四：获取校验结果参数

###### 步骤五：继续完成登录流程

###### ER-预期结果：1：H5端极验组件适配良好；2：触摸交互响应灵敏；3：在不同屏幕尺寸下正常显示；4：校验成功率高；5：登录流程顺畅；

### 场景测试

#### 人机校验完整流程场景

##### TL-SSO登录人机校验完整业务流程验证场景

###### PD-前置条件：SSO服务正常；极验服务正常；短信服务可用；

###### 步骤一：用户访问需要SSO登录的页面

###### 步骤二：点击SSO登录按钮

###### 步骤三：系统调用人机校验接口获取参数

###### 步骤四：前端展示极验人机校验组件

###### 步骤五：用户完成人机校验操作

###### 步骤六：获取校验结果参数

###### 步骤七：调用发送验证码接口并传入极验参数

###### 步骤八：后端校验极验参数有效性

###### 步骤九：发送短信验证码给用户

###### 步骤十：用户输入验证码

###### 步骤十一：调用绑定登录接口完成登录

###### 步骤十二：用户成功登录系统

###### ER-预期结果：1：整个流程无缝衔接；2：人机校验有效防止机器操作；3：验证码发送成功；4：登录功能正常；5：用户体验流畅；6：安全性得到提升；7：系统性能稳定；

#### 异常处理场景

##### TL-人机校验超时和重试场景验证

###### PD-前置条件：SSO服务正常；极验服务正常；

###### 步骤一：用户开始人机校验

###### 步骤二：长时间不完成校验操作

###### 步骤三：等待校验超时

###### 步骤四：系统提示重新校验

###### 步骤五：用户重新进行人机校验

###### 步骤六：完成校验并继续登录流程

###### ER-预期结果：1：系统检测到校验超时；2：提示用户重新进行校验；3：清除之前的校验状态；4：重新校验功能正常；5：不影响后续登录流程；

##### TL-极验参数篡改检测场景验证

###### PD-前置条件：SSO服务正常；极验服务正常；

###### 步骤一：正常获取极验参数

###### 步骤二：人为篡改geetest_validate参数

###### 步骤三：调用发送验证码接口

###### 步骤四：后端检测参数篡改

###### 步骤五：拒绝请求并返回错误

###### ER-预期结果：1：后端检测到参数篡改；2：拒绝发送验证码；3：返回明确的错误信息；4：记录安全事件日志；5：防止恶意绕过校验；

#### 性能和并发场景

##### TL-SSO登录人机校验并发性能验证场景

###### PD-前置条件：SSO服务正常；极验服务正常；测试环境稳定；

###### 步骤一：模拟100个用户同时访问SSO登录

###### 步骤二：并发调用人机校验接口

###### 步骤三：同时进行人机校验操作

###### 步骤四：并发发送验证码请求

###### 步骤五：统计成功率和响应时间

###### 步骤六：分析系统性能表现

###### ER-预期结果：1：人机校验接口并发处理正常；2：极验组件加载成功率大于95%；3：验证码发送成功率大于95%；4：平均响应时间小于3秒；5：系统稳定性良好；6：无明显性能瓶颈；

### 边界测试

#### 参数边界验证

##### TL-showAgreement参数类型边界验证

###### PD-前置条件：核身服务正常；

###### 步骤一：调用核身接口传入showAgreement="true"（字符串）

###### 步骤二：调用核身接口传入showAgreement=1（数字）

###### 步骤三：调用核身接口传入showAgreement=null

###### 步骤四：检查各种参数类型的处理结果

###### ER-预期结果：1：字符串类型返回参数格式错误；2：数字类型返回参数格式错误；3：null值按默认false处理；4：只接受布尔类型参数；

##### TL-redirectUrl参数长度边界验证

###### PD-前置条件：V3账号服务正常；

###### 步骤一：传入超长redirectUrl（>2048字符）

###### 步骤二：传入空字符串redirectUrl

###### 步骤三：传入特殊字符redirectUrl

###### 步骤四：检查系统处理结果

###### ER-预期结果：1：超长URL返回参数长度错误；2：空字符串使用默认处理；3：特殊字符进行安全过滤；4：参数验证机制有效；

##### TL-手机号格式边界验证

###### PD-前置条件：短信服务正常；

###### 步骤一：使用无效格式手机号发送短信

###### 步骤二：使用边界长度手机号发送短信

###### 步骤三：使用特殊字符手机号发送短信

###### 步骤四：验证手机号格式校验

###### ER-预期结果：1：无效格式返回手机号格式错误；2：边界长度正确处理；3：特殊字符被拒绝；4：格式校验严格有效；

### 异常测试

#### 服务异常场景

##### TL-协议服务异常时核身功能验证

###### PD-前置条件：核身服务正常；协议服务异常；

###### 步骤一：调用核身接口设置showAgreement=true

###### 步骤二：协议服务返回异常

###### 步骤三：检查核身功能是否受影响

###### ER-预期结果：1：核身功能正常进行；2：协议展示失败不影响核身；3：记录协议服务异常日志；4：用户收到友好提示；

##### TL-扣费系统异常时短信发送验证

###### PD-前置条件：短信服务正常；扣费系统异常；

###### 步骤一：使用国际手机号发送短信

###### 步骤二：扣费系统无法正常扣费

###### 步骤三：检查短信发送处理

###### ER-预期结果：1：短信发送被阻止；2：返回扣费异常错误；3：不发送短信避免损失；4：记录异常日志；

##### TL-极验服务间歇性异常处理验证

###### PD-前置条件：SSO服务正常；极验服务间歇性异常；

###### 步骤一：用户开始SSO登录流程

###### 步骤二：人机校验过程中极验服务异常

###### 步骤三：系统检测异常并处理

###### ER-预期结果：1：系统检测到服务异常；2：自动切换到降级模式；3：用户可继续登录流程；4：异常处理透明化；

### 安全测试

#### 参数安全验证

##### TL-核身接口SQL注入安全验证

###### PD-前置条件：核身服务正常；

###### 步骤一：在showAgreement参数中注入SQL语句

###### 步骤二：在其他参数中注入恶意代码

###### 步骤三：提交请求检查系统响应

###### ER-预期结果：1：系统拒绝恶意参数；2：返回参数格式错误；3：不执行注入代码；4：记录安全事件；

##### TL-极验参数篡改安全验证

###### PD-前置条件：SSO服务正常；极验服务正常；

###### 步骤一：获取正常极验参数

###### 步骤二：篡改关键校验参数

###### 步骤三：尝试绕过人机校验

###### ER-预期结果：1：系统检测到参数篡改；2：校验失败拒绝请求；3：记录安全事件日志；4：防止恶意绕过；

##### TL-国际短信费用攻击防护验证

###### PD-前置条件：短信服务正常；扣费系统正常；

###### 步骤一：短时间内大量发送国际短信

###### 步骤二：尝试绕过扣费机制

###### 步骤三：检查系统防护措施

###### ER-预期结果：1：系统检测到异常行为；2：触发频率限制机制；3：阻止恶意消费；4：保护客户资金安全；

### 性能测试

#### 接口性能验证

##### TL-核身接口协议展示性能验证

###### PD-前置条件：测试环境稳定；核身服务正常；

###### 步骤一：并发调用核身接口（showAgreement=true）

###### 步骤二：并发调用核身接口（showAgreement=false）

###### 步骤三：对比两种情况的响应时间

###### ER-预期结果：1：协议展示不显著影响性能；2：响应时间差异小于500ms；3：并发处理能力正常；

##### TL-国际短信发送性能验证

###### PD-前置条件：测试环境稳定；短信服务正常；

###### 步骤一：批量发送国际短信

###### 步骤二：批量发送国内短信

###### 步骤三：对比发送性能差异

###### ER-预期结果：1：国际短信发送成功率>95%；2：平均发送时间<10秒；3：扣费计算准确及时；

##### TL-SSO人机校验性能验证

###### PD-前置条件：测试环境稳定；相关服务正常；

###### 步骤一：并发进行人机校验

###### 步骤二：测试极验组件加载时间

###### 步骤三：测试校验响应时间

###### ER-预期结果：1：组件加载时间<2秒；2：校验响应时间<3秒；3：并发处理能力满足需求；

## 冒烟测试用例

### 需求1核心功能冒烟

#### MYTL-个人核身接口showAgreement基本功能验证

##### PD-前置条件：核身服务正常；协议服务可用；

##### 步骤一：调用个人核身网页接口设置showAgreement=true

##### 步骤二：检查协议展示效果

##### 步骤三：验证核身功能正常

##### ER-预期结果：1：接口调用成功；2：协议正常展示；3：核身流程正常；

#### MYTL-showAgreement参数默认值验证

##### PD-前置条件：核身服务正常；

##### 步骤一：调用核身接口不传showAgreement参数

##### 步骤二：检查默认行为

##### ER-预期结果：1：接口调用成功；2：默认不展示协议；3：功能正常；

### 需求2核心功能冒烟

#### MYTL-V3账号解绑微信小程序跳转验证

##### PD-前置条件：V3账号服务正常；微信小程序环境；

##### 步骤一：调用解绑接口设置redirectUrl="wechat://back"

##### 步骤二：执行解绑操作

##### 步骤三：验证跳转效果

##### ER-预期结果：1：解绑成功；2：成功唤起微信back方法；3：跳转正常；

### 需求3核心功能冒烟

#### MYTL-国际短信扣费基本验证

##### PD-前置条件：短信服务正常；扣费系统正常；

##### 步骤一：使用国际手机号发送短信

##### 步骤二：检查扣费记录

##### ER-预期结果：1：短信发送成功；2：按国际标准扣费；3：扣费准确；

#### MYTL-国内短信扣费基本验证

##### PD-前置条件：短信服务正常；扣费系统正常；

##### 步骤一：使用国内手机号发送短信

##### 步骤二：检查扣费记录

##### ER-预期结果：1：短信发送成功；2：按国内标准扣费；3：扣费准确；

### 需求4核心功能冒烟

#### MYTL-企业法人刷脸加四要素认证验证

##### PD-前置条件：企业实名服务正常；法人刷脸服务可用；

##### 步骤一：进行法人刷脸认证

##### 步骤二：完成四要素验证

##### 步骤三：检查认证方式记录

##### ER-预期结果：1：认证流程正常；2：认证方式正确显示；3：认证成功；

### 需求5核心功能冒烟

#### MYTL-SSO登录人机校验基本验证

##### PD-前置条件：SSO服务正常；极验服务正常；

##### 步骤一：完成人机校验

##### 步骤二：发送验证码

##### 步骤三：完成登录

##### ER-预期结果：1：人机校验通过；2：验证码发送成功；3：登录成功；

#### MYTL-极验降级处理验证

##### PD-前置条件：SSO服务正常；极验服务异常；

##### 步骤一：访问登录页面

##### 步骤二：检查降级处理

##### 步骤三：完成登录

##### ER-预期结果：1：自动降级；2：不展示极验；3：登录正常；

## 线上验证用例

### 需求1线上验证

#### PATL-个人核身接口showAgreement线上功能验证

##### PD-前置条件：生产环境；核身服务正常；

##### 步骤一：调用核身接口设置showAgreement=true

##### 步骤二：验证协议展示和核身流程

##### ER-预期结果：1：接口正常响应；2：协议正确展示；3：核身功能正常；4：用户体验良好；

#### PATL-多核身接口协议展示一致性验证

##### PD-前置条件：生产环境；

##### 步骤一：分别调用四个核身接口

##### 步骤二：设置showAgreement=true

##### 步骤三：验证协议展示一致性

##### ER-预期结果：1：所有接口功能正常；2：协议展示一致；3：参数优先级正确；

### 需求2线上验证

#### PATL-V3账号解绑微信小程序跳转线上验证

##### PD-前置条件：生产环境；微信小程序；

##### 步骤一：执行解绑操作设置redirectUrl="wechat://back"

##### 步骤二：验证解绑和跳转功能

##### ER-预期结果：1：解绑操作成功；2：微信back方法正常唤起；3：用户体验流畅；

### 需求3线上验证

#### PATL-国际短信扣费功能线上验证

##### PD-前置条件：生产环境；客户账户正常；

##### 步骤一：使用国际手机号发送短信

##### 步骤二：验证扣费记录准确性

##### ER-预期结果：1：短信正常发送；2：扣费按国际标准执行；3：客户账单准确；

#### PATL-白名单客户扣费验证

##### PD-前置条件：生产环境；白名单客户；

##### 步骤一：白名单客户发送国际短信

##### 步骤二：验证扣费标准

##### ER-预期结果：1：短信正常发送；2：按普通短信扣费；3：白名单功能生效；

### 需求4线上验证

#### PATL-企业法人认证方式调整线上验证

##### PD-前置条件：生产环境；企业用户；

##### 步骤一：进行企业法人认证

##### 步骤二：检查认证记录和出证信息

##### ER-预期结果：1：认证流程正常；2：认证方式正确记录；3：出证信息准确；

### 需求5线上验证

#### PATL-SSO登录人机校验线上验证

##### PD-前置条件：生产环境；

##### 步骤一：完成人机校验流程

##### 步骤二：验证登录功能

##### ER-预期结果：1：人机校验正常工作；2：登录流程顺畅；3：用户体验良好；

#### PATL-极验降级功能线上验证

##### PD-前置条件：生产环境；极验服务可能异常；

##### 步骤一：模拟极验服务异常

##### 步骤二：验证降级处理效果

##### ER-预期结果：1：系统自动降级；2：登录功能不受影响；3：用户无感知切换；